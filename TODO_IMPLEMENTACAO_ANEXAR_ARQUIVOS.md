# TODO: Implementação de Anexar Arquivos no Chat

## Objetivo
Replicar a funcionalidade de anexar arquivos do projeto Google ADK Web para o projeto Next.js PrimeAI, permitindo que usuários anexem arquivos (imagens, documentos, etc.) no chat e que o agente Python consiga processar esses arquivos.

## Análise da Implementação Atual

### Google ADK Web (Angular)
- **Arquivo principal**: `adk-web/src/app/components/chat/chat.component.ts`
- **Template**: `adk-web/src/app/components/chat/chat.component.html`
- **Funcionalidades identificadas**:
  - Upload múltiplo de arquivos via input file
  - Preview de imagens e arquivos
  - Conversão de arquivos para base64
  - Envio de arquivos como `inlineData` no formato `types.Part`
  - Suporte a diferentes tipos MIME

### PrimeAI (Next.js)
- **Arquivo principal**: `primeai/components/chat/chat-interface.tsx`
- **API**: `primeai/app/api/chat/route.ts`
- **Agente**: `credit_agent_copy/agent.py`
- **Status atual**: Não possui funcionalidade de anexar arquivos

## Tarefas de Implementação

### 1. Frontend - Componente de Upload de Arquivos

#### 1.1 Atualizar tipos TypeScript
**Arquivo**: `primeai/components/chat/types.ts`

```typescript
// Adicionar ao ChatMessage
export interface ChatMessage {
  id: string;
  content: {
    text: string;
    echarts?: any;
    attachments?: FileAttachment[]; // NOVO
  };
  role: "user" | "assistant" | "system";
  timestamp: string;
}

// Novos tipos
export interface FileAttachment {
  file: File;
  url: string;
  base64?: string;
  mimeType: string;
}

export interface InlineData {
  displayName: string;
  data: string; // base64
  mimeType: string;
}

// Atualizar ApiResponsePart
export interface ApiResponsePart {
  text?: string;
  inlineData?: InlineData; // NOVO
  functionCall?: {
    name: string;
    args: {
      [key: string]: any;
    };
  };
  functionResponse?: {
    name: string;
    response: {
      result: string;
    };
  };
}
```

#### 1.2 Criar componente de upload de arquivos
**Arquivo**: `primeai/components/chat/file-upload.tsx`

```typescript
"use client";

import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { X, Paperclip, Image, FileText } from "lucide-react";
import { FileAttachment } from "./types";

interface FileUploadProps {
  selectedFiles: FileAttachment[];
  onFilesSelected: (files: FileAttachment[]) => void;
  onFileRemoved: (index: number) => void;
  disabled?: boolean;
}

export default function FileUpload({
  selectedFiles,
  onFilesSelected,
  onFileRemoved,
  disabled = false
}: FileUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    const newFiles: FileAttachment[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const url = URL.createObjectURL(file);
      
      newFiles.push({
        file,
        url,
        mimeType: file.type
      });
    }

    onFilesSelected([...selectedFiles, ...newFiles]);
    
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeFile = (index: number) => {
    // Revoke object URL to prevent memory leaks
    URL.revokeObjectURL(selectedFiles[index].url);
    onFileRemoved(index);
  };

  return (
    <div className="space-y-2">
      {/* File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*,.pdf,.doc,.docx,.txt,.csv,.xlsx"
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
      />
      
      {/* Upload Button */}
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={() => fileInputRef.current?.click()}
        disabled={disabled}
        className="flex items-center gap-2"
      >
        <Paperclip className="h-4 w-4" />
        Anexar arquivo
      </Button>

      {/* File Previews */}
      {selectedFiles.length > 0 && (
        <div className="flex flex-wrap gap-2 p-2 border rounded-md bg-muted/50">
          {selectedFiles.map((fileAttachment, index) => (
            <div
              key={index}
              className="relative flex items-center gap-2 p-2 border rounded bg-background"
            >
              {fileAttachment.file.type.startsWith('image/') ? (
                <div className="relative">
                  <img
                    src={fileAttachment.url}
                    alt="Preview"
                    className="w-12 h-12 object-cover rounded"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute -top-2 -right-2 h-5 w-5 p-0"
                    onClick={() => removeFile(index)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <FileText className="h-8 w-8 text-muted-foreground" />
                  <div className="flex flex-col">
                    <span className="text-sm font-medium truncate max-w-[100px]">
                      {fileAttachment.file.name}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {(fileAttachment.file.size / 1024).toFixed(1)} KB
                    </span>
                  </div>
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="h-5 w-5 p-0"
                    onClick={() => removeFile(index)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
```

#### 1.3 Atualizar ChatInterface
**Arquivo**: `primeai/components/chat/chat-interface.tsx`

Adicionar estado para arquivos selecionados:
```typescript
const [selectedFiles, setSelectedFiles] = useState<FileAttachment[]>([]);
```

Adicionar função para converter arquivo para base64:
```typescript
const readFileAsBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      const base64Data = e.target.result.split(',')[1];
      resolve(base64Data);
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};
```

### 2. Backend - API de Chat

#### 2.1 Atualizar rota da API
**Arquivo**: `primeai/app/api/chat/route.ts`

Modificar a função `POST` para processar arquivos anexados:

```typescript
// Adicionar após linha 262 (requestBody = await request.json())
// Processar arquivos anexados se existirem
if (requestBody.new_message?.parts) {
  for (let i = 0; i < requestBody.new_message.parts.length; i++) {
    const part = requestBody.new_message.parts[i];
    
    // Se a parte contém dados de arquivo
    if (part.inlineData) {
      // Validar tipo MIME
      const allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'application/pdf', 'text/plain', 'text/csv',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];
      
      if (!allowedTypes.includes(part.inlineData.mimeType)) {
        return NextResponse.json(
          [{
            id: AppUtils.generateUniqueId('error-file-type'),
            content: {
              parts: [{
                text: `❌ **Tipo de arquivo não suportado**\n\nO arquivo "${part.inlineData.displayName}" tem o tipo "${part.inlineData.mimeType}" que não é suportado.\n\nTipos suportados: imagens (JPEG, PNG, GIF, WebP), PDF, TXT, CSV, DOCX, XLSX.`
              }],
              role: "model"
            },
            timestamp: Date.now()
          }],
          { status: 200 }
        );
      }
      
      // Validar tamanho do arquivo (máximo 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB em bytes
      const fileSize = (part.inlineData.data.length * 3) / 4; // Aproximação do tamanho original
      
      if (fileSize > maxSize) {
        return NextResponse.json(
          [{
            id: AppUtils.generateUniqueId('error-file-size'),
            content: {
              parts: [{
                text: `❌ **Arquivo muito grande**\n\nO arquivo "${part.inlineData.displayName}" é muito grande (${(fileSize / 1024 / 1024).toFixed(1)}MB).\n\nTamanho máximo permitido: 10MB.`
              }],
              role: "model"
            },
            timestamp: Date.now()
          }],
          { status: 200 }
        );
      }
    }
  }
}
```

### 3. Agente Python - Processamento de Arquivos

#### 3.1 Verificar se o agente já suporta arquivos
**Arquivo**: `credit_agent_copy/agent.py`

O agente já está configurado para receber arquivos através do `new_message.parts` que pode conter `inlineData`. Não são necessárias modificações no agente para esta primeira implementação.

#### 3.2 Implementar salvamento de artifacts (Passo 2 - Futuro)
Baseado na documentação `artifacts_doc.txt`, implementar:

```python
# Exemplo de como salvar arquivos como artifacts
async def save_uploaded_file_as_artifact(context: CallbackContext, file_data: bytes, filename: str, mime_type: str):
    """Salva arquivo enviado pelo usuário como artifact."""
    file_artifact = types.Part.from_bytes(
        data=file_data,
        mime_type=mime_type
    )
    
    try:
        version = await context.save_artifact(filename=filename, artifact=file_artifact)
        print(f"Arquivo '{filename}' salvo como artifact versão {version}")
        return version
    except Exception as e:
        print(f"Erro ao salvar artifact: {e}")
        return None
```

### 4. Integração e Testes

#### 4.1 Atualizar ChatMessage component
**Arquivo**: `primeai/components/chat/chat-message.tsx`

Adicionar suporte para exibir arquivos anexados:

```typescript
// Adicionar renderização de attachments
{message.content.attachments && (
  <div className="flex flex-wrap gap-2 mb-2">
    {message.content.attachments.map((attachment, index) => (
      <div key={index} className="border rounded p-2 bg-muted/50">
        {attachment.file.type.startsWith('image/') ? (
          <img 
            src={attachment.url} 
            alt="Attachment" 
            className="max-w-xs max-h-32 object-cover rounded"
          />
        ) : (
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span className="text-sm">{attachment.file.name}</span>
          </div>
        )}
      </div>
    ))}
  </div>
)}
```

#### 4.2 Testes necessários
1. **Upload de diferentes tipos de arquivo**:
   - Imagens (JPEG, PNG, GIF, WebP)
   - Documentos (PDF, DOCX, TXT, CSV, XLSX)
   
2. **Validações**:
   - Tamanho máximo de arquivo (10MB)
   - Tipos de arquivo permitidos
   - Múltiplos arquivos
   
3. **Integração com agente**:
   - Verificar se arquivos chegam corretamente no agente
   - Testar processamento de imagens
   - Testar processamento de documentos

### 5. Melhorias Futuras (Passo 2)

#### 5.1 Implementar Artifacts Service
- Configurar `InMemoryArtifactService` ou `GcsArtifactService`
- Salvar arquivos enviados como artifacts
- Permitir download de arquivos gerados pelo agente

#### 5.2 Funcionalidades avançadas
- Drag & drop de arquivos
- Progress bar para upload
- Preview melhorado para diferentes tipos de arquivo
- Compressão automática de imagens grandes
- OCR para extrair texto de imagens

## Ordem de Implementação Recomendada

1. **Fase 1 - Funcionalidade Básica**:
   - Atualizar tipos TypeScript
   - Criar componente FileUpload
   - Integrar no ChatInterface
   - Atualizar API para validar arquivos
   - Testes básicos

2. **Fase 2 - Artifacts e Melhorias**:
   - Implementar Artifacts Service
   - Adicionar funcionalidades avançadas
   - Testes completos

## Arquivos a serem Modificados/Criados

### Novos arquivos:
- `primeai/components/chat/file-upload.tsx`

### Arquivos a modificar:
- `primeai/components/chat/types.ts`
- `primeai/components/chat/chat-interface.tsx`
- `primeai/components/chat/chat-message.tsx`
- `primeai/app/api/chat/route.ts`

### Arquivos de referência (não modificar):
- `adk-web/src/app/components/chat/chat.component.ts`
- `adk-web/src/app/components/chat/chat.component.html`
- `credit_agent_copy/agent.py`
- `artifacts_doc.txt`

## Considerações Técnicas

1. **Segurança**: Validar tipos MIME e tamanhos de arquivo
2. **Performance**: Limitar número e tamanho de arquivos
3. **Memória**: Revogar URLs de objeto para evitar vazamentos
4. **UX**: Feedback visual claro para upload e erros
5. **Compatibilidade**: Manter compatibilidade com agente existente

## Estimativa de Tempo

- **Fase 1**: 2-3 dias de desenvolvimento + 1 dia de testes
- **Fase 2**: 3-4 dias de desenvolvimento + 2 dias de testes

Total estimado: 8-10 dias de trabalho.
