from google.adk.agents import Agent, LlmAgent
from .tools import postgres_query_tool
from .tools import rag_query
from google.genai import types
from .prompt import ESPECIALISTA_CREDITO_PROMPT
from google.adk.agents.callback_context import CallbackContext
from google.adk.models.lite_llm import LiteLlm
from datetime import datetime
from zoneinfo import ZoneInfo
from typing import Optional
from google.adk.planners.built_in_planner import BuiltInPlanner
from google.adk.code_executors import BuiltInCodeExecutor
from google.adk.tools import agent_tool


date_today = datetime.now(ZoneInfo("America/Sao_Paulo"))

def setup_credito_agent_callback(callback_context: CallbackContext) -> Optional[types.Content]:
    """
    Callback function to setup the credit agent with database schema information.
    
    Returns:
        Optional[types.Content]: None to allow normal agent execution, 
                               or Content to override the agent's response
    """
    agent_name = callback_context.agent_name
    invocation_id = callback_context.invocation_id
    current_state = callback_context.state.to_dict()
    
    print(f"\n[Callback] Entering agent: {agent_name} (Inv: {invocation_id})")
    print(f"[Callback] Current State: {current_state}")
    
    # Verificar se precisa buscar o schema do banco
    if "schema_overview" not in callback_context.state:
        try:
            print("[Callback] Buscando schema do banco de dados...")
            
            # Query SQL unificada para obter schema
            schema_query = """
                SELECT json_agg(table_info) AS schema_json
                FROM (
                    SELECT
                        tbl.table_name,
                        tbl.table_description,
                        json_agg(
                            json_build_object(
                                'column_name', col.column_name,
                                'data_type', col.data_type,
                                'column_description', col.column_description,
                                'foreign_key', CASE
                                    WHEN fk.target_table IS NOT NULL THEN json_build_object(
                                        'target_table', fk.target_table,
                                        'target_column', fk.target_column
                                    )
                                    ELSE NULL
                                END
                            )
                            ORDER BY col.column_name
                        ) AS columns
                    FROM (
                        SELECT
                            c.relname AS table_name,
                            obj_description(c.oid) AS table_description
                        FROM
                            pg_class c
                        JOIN
                            pg_namespace n ON n.oid = c.relnamespace
                        WHERE
                            c.relkind = 'r'
                            AND n.nspname = 'public'
                            AND c.relname NOT IN (
                                'cadastro_fundos',
                                'cotacoes_historicas_b3',
                                'cvm_informes_diarios_fi'
                            )
                    ) AS tbl
                    LEFT JOIN (
                        SELECT
                            a.attrelid::regclass::text AS table_name,
                            a.attname AS column_name,
                            pg_catalog.format_type(a.atttypid, a.atttypmod) AS data_type,
                            col_description(a.attrelid, a.attnum) AS column_description
                        FROM
                            pg_attribute a
                        WHERE
                            a.attnum > 0 AND NOT a.attisdropped
                    ) AS col ON tbl.table_name = col.table_name
                    LEFT JOIN (
                        SELECT
                            tc.table_name AS source_table,
                            kcu.column_name AS source_column,
                            ccu.table_name AS target_table,
                            ccu.column_name AS target_column
                        FROM
                            information_schema.table_constraints AS tc
                        JOIN
                            information_schema.key_column_usage AS kcu
                                ON tc.constraint_name = kcu.constraint_name
                                AND tc.table_schema = kcu.table_schema
                        JOIN
                            information_schema.constraint_column_usage AS ccu
                                ON ccu.constraint_name = tc.constraint_name
                                AND ccu.table_schema = tc.table_schema
                        WHERE
                            tc.constraint_type = 'FOREIGN KEY'
                    ) AS fk
                    ON tbl.table_name = fk.source_table AND col.column_name = fk.source_column
                    GROUP BY tbl.table_name, tbl.table_description
                    ORDER BY tbl.table_name
                ) AS table_info;

            """

            # Executa a query
            schema_result = postgres_query_tool(schema_query)
            callback_context.state["schema_overview"] = schema_result
            print("[Callback] Schema obtido e armazenado no state com sucesso!")

        except Exception as e:
            error_msg = f"Erro ao buscar schema: {e}"
            callback_context.state["schema_overview"] = error_msg
            print(f"[Callback] ERRO: {error_msg}")

    # Tentar injetar o schema no contexto do agente
    try:
        # Acessar o agente através do callback_context
        if hasattr(callback_context, '_invocation_context') and callback_context._invocation_context:
            agent = callback_context._invocation_context.agent
            if hasattr(agent, 'instruction'):
                schema_info = callback_context.state.get("schema_overview", "Schema não disponível")
                
                # Adicionar schema ao instruction se ainda não foi adicionado
                if "=== ESTRUTURA DO BANCO DE DADOS ===" not in agent.instruction:
                    agent.instruction += (
                        f"\n\n=== ESTRUTURA DO BANCO DE DADOS ===\n"
                        f"{schema_info}\n"
                        f"=== FIM DA ESTRUTURA DO BANCO ===\n\n"
                        f"Use essas informações sobre a estrutura do banco para construir "
                        f"queries SQL precisas quando necessário."
                    )
                    print("[Callback] Schema injetado no instruction do agente!")
                else:
                    print("[Callback] Schema já estava presente no instruction")
            else:
                print("[Callback] Agent não possui atributo 'instruction'")
        else:
            print("[Callback] _invocation_context não disponível no callback")
            
    except Exception as e:
        print(f"[Callback] Erro ao injetar schema no agent: {e}")

    # Verificar se deve pular a execução do agente (exemplo de controle)
    if current_state.get("skip_agent", False):
        print(f"[Callback] Pulando execução do agente {agent_name}")
        return types.Content(
            parts=[types.Part(text=f"Agente {agent_name} foi pulado por configuração no state.")],
            role="model"
        )   
    
    # Retornar None permite que o agente execute normalmente
    print(f"[Callback] Prosseguindo com execução normal do agente {agent_name}")
    return None

coding_agent = Agent(
    model='gemini-2.0-flash',
    name='CodeAgent',
    instruction="""
    You're a specialist in Code Execution
    """,
    code_executor=BuiltInCodeExecutor(),
)

especialista_credito = LlmAgent(
   model="gemini-2.5-flash-preview-05-20",
   name="especialista_credito",
   global_instruction=(
        f"""
        Data de hoje: {date_today}
        """
        ),
   instruction=ESPECIALISTA_CREDITO_PROMPT,
   description="Especialista em crédito (CRI, CRA, Debentures e Titulos públicos) do mercado financeiro.",
   tools=[postgres_query_tool, agent_tool.AgentTool(agent=coding_agent)],
   before_agent_callback=setup_credito_agent_callback,
   generate_content_config=types.GenerateContentConfig(max_output_tokens=2500),
   planner=BuiltInPlanner(
        thinking_config=types.ThinkingConfig(
            thinking_budget=1024,
        ),  
    )
)

root_agent = especialista_credito