from datetime import datetime
import pytz


def get_sao_paulo_datetime() -> str:
    """
    Retorna o datetime atual no horário de São Paulo (America/Sao_Paulo).
    
    Returns:
        str: Data e hora atual formatada no horário de São Paulo.
    """
    print("--- Tool: get_sao_paulo_datetime chamada ---")
    
    try:
        # Define o timezone de São Paulo
        sao_paulo_tz = pytz.timezone('America/Sao_Paulo')
        
        # Obtém o datetime atual no timezone de São Paulo
        now_sao_paulo = datetime.now(sao_paulo_tz)
        
        # Formata a data e hora
        formatted_datetime = now_sao_paulo.strftime('%Y-%m-%d %H:%M:%S %Z')
        
        print(f"--- Tool: Datetime atual em São Paulo: {formatted_datetime} ---")
        
        return f"Data e hora atual em São Paulo: {formatted_datetime}"
        
    except Exception as e:
        error_msg = f"Erro ao obter datetime de São Paulo: {e}"
        print(f"--- Tool: {error_msg} ---")
        return error_msg
