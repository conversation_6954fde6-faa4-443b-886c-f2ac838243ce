import psycopg2
from psycopg2.extras import RealDictCursor

def postgres_query_tool(query: str) -> str:
    """
    Executa uma query SQL em um banco PostgreSQL e retorna os resultados.

    Args:
        query (str): Instrução SQL a ser executada.

    Returns:
        str: Resultados formatados da consulta.
    """
    print(f"--- Tool: postgres_query_tool chamada com query: {query} ---")

    # Configuração de conexão — personalize com seus dados:
    conn_params = {
        "host": "aws-0-sa-east-1.pooler.supabase.com",
        "port": 5432,
        "dbname": "postgres",
        "user": "postgres.tshkxxrdecovaxsxibpa",
        "password": "251085VASCo!#"
    }

    try:
        # Conectar ao banco
        with psycopg2.connect(**conn_params) as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute(query)
                results = cur.fetchall()

        # Formatando resultado como string legível
        if not results:
            return "Nenhum dado encontrado."
        
        # Formata em tabela de texto
        col_names = results[0].keys()
        linhas = [", ".join(col_names)]
        for row in results:
            linhas.append(", ".join(str(row[col]) for col in col_names))
        
        return "\n".join(linhas)

    except Exception as e:
        return f"Erro ao executar a query: {e}"