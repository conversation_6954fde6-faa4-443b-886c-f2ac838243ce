"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gcp-metadata";
exports.ids = ["vendor-chunks/gcp-metadata"];
exports.modules = {

/***/ "(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js":
/*!**************************************************************!*\
  !*** ./node_modules/gcp-metadata/build/src/gcp-residency.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GCE_LINUX_BIOS_PATHS = void 0;\nexports.isGoogleCloudServerless = isGoogleCloudServerless;\nexports.isGoogleComputeEngineLinux = isGoogleComputeEngineLinux;\nexports.isGoogleComputeEngineMACAddress = isGoogleComputeEngineMACAddress;\nexports.isGoogleComputeEngine = isGoogleComputeEngine;\nexports.detectGCPResidency = detectGCPResidency;\nconst fs_1 = __webpack_require__(/*! fs */ \"fs\");\nconst os_1 = __webpack_require__(/*! os */ \"os\");\n/**\n * Known paths unique to Google Compute Engine Linux instances\n */\nexports.GCE_LINUX_BIOS_PATHS = {\n    BIOS_DATE: '/sys/class/dmi/id/bios_date',\n    BIOS_VENDOR: '/sys/class/dmi/id/bios_vendor',\n};\nconst GCE_MAC_ADDRESS_REGEX = /^42:01/;\n/**\n * Determines if the process is running on a Google Cloud Serverless environment (Cloud Run or Cloud Functions instance).\n *\n * Uses the:\n * - {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n * - {@link https://cloud.google.com/functions/docs/env-var Cloud Functions environment variables}.\n *\n * @returns {boolean} `true` if the process is running on GCP serverless, `false` otherwise.\n */\nfunction isGoogleCloudServerless() {\n    /**\n     * `CLOUD_RUN_JOB` is used for Cloud Run Jobs\n     * - See {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n     *\n     * `FUNCTION_NAME` is used in older Cloud Functions environments:\n     * - See {@link https://cloud.google.com/functions/docs/env-var Python 3.7 and Go 1.11}.\n     *\n     * `K_SERVICE` is used in Cloud Run and newer Cloud Functions environments:\n     * - See {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n     * - See {@link https://cloud.google.com/functions/docs/env-var Cloud Functions newer runtimes}.\n     */\n    const isGFEnvironment = process.env.CLOUD_RUN_JOB ||\n        process.env.FUNCTION_NAME ||\n        process.env.K_SERVICE;\n    return !!isGFEnvironment;\n}\n/**\n * Determines if the process is running on a Linux Google Compute Engine instance.\n *\n * @returns {boolean} `true` if the process is running on Linux GCE, `false` otherwise.\n */\nfunction isGoogleComputeEngineLinux() {\n    if ((0, os_1.platform)() !== 'linux')\n        return false;\n    try {\n        // ensure this file exist\n        (0, fs_1.statSync)(exports.GCE_LINUX_BIOS_PATHS.BIOS_DATE);\n        // ensure this file exist and matches\n        const biosVendor = (0, fs_1.readFileSync)(exports.GCE_LINUX_BIOS_PATHS.BIOS_VENDOR, 'utf8');\n        return /Google/.test(biosVendor);\n    }\n    catch (_a) {\n        return false;\n    }\n}\n/**\n * Determines if the process is running on a Google Compute Engine instance with a known\n * MAC address.\n *\n * @returns {boolean} `true` if the process is running on GCE (as determined by MAC address), `false` otherwise.\n */\nfunction isGoogleComputeEngineMACAddress() {\n    const interfaces = (0, os_1.networkInterfaces)();\n    for (const item of Object.values(interfaces)) {\n        if (!item)\n            continue;\n        for (const { mac } of item) {\n            if (GCE_MAC_ADDRESS_REGEX.test(mac)) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n/**\n * Determines if the process is running on a Google Compute Engine instance.\n *\n * @returns {boolean} `true` if the process is running on GCE, `false` otherwise.\n */\nfunction isGoogleComputeEngine() {\n    return isGoogleComputeEngineLinux() || isGoogleComputeEngineMACAddress();\n}\n/**\n * Determines if the process is running on Google Cloud Platform.\n *\n * @returns {boolean} `true` if the process is running on GCP, `false` otherwise.\n */\nfunction detectGCPResidency() {\n    return isGoogleCloudServerless() || isGoogleComputeEngine();\n}\n//# sourceMappingURL=gcp-residency.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gcp-metadata/build/src/index.js":
/*!******************************************************!*\
  !*** ./node_modules/gcp-metadata/build/src/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/**\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.gcpResidencyCache = exports.METADATA_SERVER_DETECTION = exports.HEADERS = exports.HEADER_VALUE = exports.HEADER_NAME = exports.SECONDARY_HOST_ADDRESS = exports.HOST_ADDRESS = exports.BASE_PATH = void 0;\nexports.instance = instance;\nexports.project = project;\nexports.universe = universe;\nexports.bulk = bulk;\nexports.isAvailable = isAvailable;\nexports.resetIsAvailableCache = resetIsAvailableCache;\nexports.getGCPResidency = getGCPResidency;\nexports.setGCPResidency = setGCPResidency;\nexports.requestTimeout = requestTimeout;\nconst gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/src/index.js\");\nconst jsonBigint = __webpack_require__(/*! json-bigint */ \"(rsc)/./node_modules/json-bigint/index.js\");\nconst gcp_residency_1 = __webpack_require__(/*! ./gcp-residency */ \"(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js\");\nconst logger = __webpack_require__(/*! google-logging-utils */ \"(rsc)/./node_modules/google-logging-utils/build/src/index.js\");\nexports.BASE_PATH = '/computeMetadata/v1';\nexports.HOST_ADDRESS = 'http://***************';\nexports.SECONDARY_HOST_ADDRESS = 'http://metadata.google.internal.';\nexports.HEADER_NAME = 'Metadata-Flavor';\nexports.HEADER_VALUE = 'Google';\nexports.HEADERS = Object.freeze({ [exports.HEADER_NAME]: exports.HEADER_VALUE });\nconst log = logger.log('gcp metadata');\n/**\n * Metadata server detection override options.\n *\n * Available via `process.env.METADATA_SERVER_DETECTION`.\n */\nexports.METADATA_SERVER_DETECTION = Object.freeze({\n    'assume-present': \"don't try to ping the metadata server, but assume it's present\",\n    none: \"don't try to ping the metadata server, but don't try to use it either\",\n    'bios-only': \"treat the result of a BIOS probe as canonical (don't fall back to pinging)\",\n    'ping-only': 'skip the BIOS probe, and go straight to pinging',\n});\n/**\n * Returns the base URL while taking into account the GCE_METADATA_HOST\n * environment variable if it exists.\n *\n * @returns The base URL, e.g., http://***************/computeMetadata/v1.\n */\nfunction getBaseUrl(baseUrl) {\n    if (!baseUrl) {\n        baseUrl =\n            process.env.GCE_METADATA_IP ||\n                process.env.GCE_METADATA_HOST ||\n                exports.HOST_ADDRESS;\n    }\n    // If no scheme is provided default to HTTP:\n    if (!/^https?:\\/\\//.test(baseUrl)) {\n        baseUrl = `http://${baseUrl}`;\n    }\n    return new URL(exports.BASE_PATH, baseUrl).href;\n}\n// Accepts an options object passed from the user to the API. In previous\n// versions of the API, it referred to a `Request` or an `Axios` request\n// options object.  Now it refers to an object with very limited property\n// names. This is here to help ensure users don't pass invalid options when\n// they  upgrade from 0.4 to 0.5 to 0.8.\nfunction validate(options) {\n    Object.keys(options).forEach(key => {\n        switch (key) {\n            case 'params':\n            case 'property':\n            case 'headers':\n                break;\n            case 'qs':\n                throw new Error(\"'qs' is not a valid configuration option. Please use 'params' instead.\");\n            default:\n                throw new Error(`'${key}' is not a valid configuration option.`);\n        }\n    });\n}\nasync function metadataAccessor(type, options = {}, noResponseRetries = 3, fastFail = false) {\n    let metadataKey = '';\n    let params = {};\n    let headers = {};\n    if (typeof type === 'object') {\n        const metadataAccessor = type;\n        metadataKey = metadataAccessor.metadataKey;\n        params = metadataAccessor.params || params;\n        headers = metadataAccessor.headers || headers;\n        noResponseRetries = metadataAccessor.noResponseRetries || noResponseRetries;\n        fastFail = metadataAccessor.fastFail || fastFail;\n    }\n    else {\n        metadataKey = type;\n    }\n    if (typeof options === 'string') {\n        metadataKey += `/${options}`;\n    }\n    else {\n        validate(options);\n        if (options.property) {\n            metadataKey += `/${options.property}`;\n        }\n        headers = options.headers || headers;\n        params = options.params || params;\n    }\n    const requestMethod = fastFail ? fastFailMetadataRequest : gaxios_1.request;\n    const req = {\n        url: `${getBaseUrl()}/${metadataKey}`,\n        headers: { ...exports.HEADERS, ...headers },\n        retryConfig: { noResponseRetries },\n        params,\n        responseType: 'text',\n        timeout: requestTimeout(),\n    };\n    log.info('instance request %j', req);\n    const res = await requestMethod(req);\n    log.info('instance metadata is %s', res.data);\n    // NOTE: node.js converts all incoming headers to lower case.\n    if (res.headers[exports.HEADER_NAME.toLowerCase()] !== exports.HEADER_VALUE) {\n        throw new Error(`Invalid response from metadata service: incorrect ${exports.HEADER_NAME} header. Expected '${exports.HEADER_VALUE}', got ${res.headers[exports.HEADER_NAME.toLowerCase()] ? `'${res.headers[exports.HEADER_NAME.toLowerCase()]}'` : 'no header'}`);\n    }\n    if (typeof res.data === 'string') {\n        try {\n            return jsonBigint.parse(res.data);\n        }\n        catch (_a) {\n            /* ignore */\n        }\n    }\n    return res.data;\n}\nasync function fastFailMetadataRequest(options) {\n    var _a;\n    const secondaryOptions = {\n        ...options,\n        url: (_a = options.url) === null || _a === void 0 ? void 0 : _a.toString().replace(getBaseUrl(), getBaseUrl(exports.SECONDARY_HOST_ADDRESS)),\n    };\n    // We race a connection between DNS/IP to metadata server. There are a couple\n    // reasons for this:\n    //\n    // 1. the DNS is slow in some GCP environments; by checking both, we might\n    //    detect the runtime environment signficantly faster.\n    // 2. we can't just check the IP, which is tarpitted and slow to respond\n    //    on a user's local machine.\n    //\n    // Additional logic has been added to make sure that we don't create an\n    // unhandled rejection in scenarios where a failure happens sometime\n    // after a success.\n    //\n    // Note, however, if a failure happens prior to a success, a rejection should\n    // occur, this is for folks running locally.\n    //\n    let responded = false;\n    const r1 = (0, gaxios_1.request)(options)\n        .then(res => {\n        responded = true;\n        return res;\n    })\n        .catch(err => {\n        if (responded) {\n            return r2;\n        }\n        else {\n            responded = true;\n            throw err;\n        }\n    });\n    const r2 = (0, gaxios_1.request)(secondaryOptions)\n        .then(res => {\n        responded = true;\n        return res;\n    })\n        .catch(err => {\n        if (responded) {\n            return r1;\n        }\n        else {\n            responded = true;\n            throw err;\n        }\n    });\n    return Promise.race([r1, r2]);\n}\n/**\n * Obtain metadata for the current GCE instance.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const serviceAccount: {} = await instance('service-accounts/');\n * const serviceAccountEmail: string = await instance('service-accounts/default/email');\n * ```\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction instance(options) {\n    return metadataAccessor('instance', options);\n}\n/**\n * Obtain metadata for the current GCP project.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const projectId: string = await project('project-id');\n * const numericProjectId: number = await project('numeric-project-id');\n * ```\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction project(options) {\n    return metadataAccessor('project', options);\n}\n/**\n * Obtain metadata for the current universe.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const universeDomain: string = await universe('universe-domain');\n * ```\n */\nfunction universe(options) {\n    return metadataAccessor('universe', options);\n}\n/**\n * Retrieve metadata items in parallel.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const data = await bulk([\n *   {\n *     metadataKey: 'instance',\n *   },\n *   {\n *     metadataKey: 'project/project-id',\n *   },\n * ] as const);\n *\n * // data.instance;\n * // data['project/project-id'];\n * ```\n *\n * @param properties The metadata properties to retrieve\n * @returns The metadata in `metadatakey:value` format\n */\nasync function bulk(properties) {\n    const r = {};\n    await Promise.all(properties.map(item => {\n        return (async () => {\n            const res = await metadataAccessor(item);\n            const key = item.metadataKey;\n            r[key] = res;\n        })();\n    }));\n    return r;\n}\n/*\n * How many times should we retry detecting GCP environment.\n */\nfunction detectGCPAvailableRetries() {\n    return process.env.DETECT_GCP_RETRIES\n        ? Number(process.env.DETECT_GCP_RETRIES)\n        : 0;\n}\nlet cachedIsAvailableResponse;\n/**\n * Determine if the metadata server is currently available.\n */\nasync function isAvailable() {\n    if (process.env.METADATA_SERVER_DETECTION) {\n        const value = process.env.METADATA_SERVER_DETECTION.trim().toLocaleLowerCase();\n        if (!(value in exports.METADATA_SERVER_DETECTION)) {\n            throw new RangeError(`Unknown \\`METADATA_SERVER_DETECTION\\` env variable. Got \\`${value}\\`, but it should be \\`${Object.keys(exports.METADATA_SERVER_DETECTION).join('`, `')}\\`, or unset`);\n        }\n        switch (value) {\n            case 'assume-present':\n                return true;\n            case 'none':\n                return false;\n            case 'bios-only':\n                return getGCPResidency();\n            case 'ping-only':\n            // continue, we want to ping the server\n        }\n    }\n    try {\n        // If a user is instantiating several GCP libraries at the same time,\n        // this may result in multiple calls to isAvailable(), to detect the\n        // runtime environment. We use the same promise for each of these calls\n        // to reduce the network load.\n        if (cachedIsAvailableResponse === undefined) {\n            cachedIsAvailableResponse = metadataAccessor('instance', undefined, detectGCPAvailableRetries(), \n            // If the default HOST_ADDRESS has been overridden, we should not\n            // make an effort to try SECONDARY_HOST_ADDRESS (as we are likely in\n            // a non-GCP environment):\n            !(process.env.GCE_METADATA_IP || process.env.GCE_METADATA_HOST));\n        }\n        await cachedIsAvailableResponse;\n        return true;\n    }\n    catch (e) {\n        const err = e;\n        if (process.env.DEBUG_AUTH) {\n            console.info(err);\n        }\n        if (err.type === 'request-timeout') {\n            // If running in a GCP environment, metadata endpoint should return\n            // within ms.\n            return false;\n        }\n        if (err.response && err.response.status === 404) {\n            return false;\n        }\n        else {\n            if (!(err.response && err.response.status === 404) &&\n                // A warning is emitted if we see an unexpected err.code, or err.code\n                // is not populated:\n                (!err.code ||\n                    ![\n                        'EHOSTDOWN',\n                        'EHOSTUNREACH',\n                        'ENETUNREACH',\n                        'ENOENT',\n                        'ENOTFOUND',\n                        'ECONNREFUSED',\n                    ].includes(err.code))) {\n                let code = 'UNKNOWN';\n                if (err.code)\n                    code = err.code;\n                process.emitWarning(`received unexpected error = ${err.message} code = ${code}`, 'MetadataLookupWarning');\n            }\n            // Failure to resolve the metadata service means that it is not available.\n            return false;\n        }\n    }\n}\n/**\n * reset the memoized isAvailable() lookup.\n */\nfunction resetIsAvailableCache() {\n    cachedIsAvailableResponse = undefined;\n}\n/**\n * A cache for the detected GCP Residency.\n */\nexports.gcpResidencyCache = null;\n/**\n * Detects GCP Residency.\n * Caches results to reduce costs for subsequent calls.\n *\n * @see setGCPResidency for setting\n */\nfunction getGCPResidency() {\n    if (exports.gcpResidencyCache === null) {\n        setGCPResidency();\n    }\n    return exports.gcpResidencyCache;\n}\n/**\n * Sets the detected GCP Residency.\n * Useful for forcing metadata server detection behavior.\n *\n * Set `null` to autodetect the environment (default behavior).\n * @see getGCPResidency for getting\n */\nfunction setGCPResidency(value = null) {\n    exports.gcpResidencyCache = value !== null ? value : (0, gcp_residency_1.detectGCPResidency)();\n}\n/**\n * Obtain the timeout for requests to the metadata server.\n *\n * In certain environments and conditions requests can take longer than\n * the default timeout to complete. This function will determine the\n * appropriate timeout based on the environment.\n *\n * @returns {number} a request timeout duration in milliseconds.\n */\nfunction requestTimeout() {\n    return getGCPResidency() ? 0 : 3000;\n}\n__exportStar(__webpack_require__(/*! ./gcp-residency */ \"(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gcp-metadata/build/src/index.js\n");

/***/ })

};
;