"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gtoken";
exports.ids = ["vendor-chunks/gtoken"];
exports.modules = {

/***/ "(rsc)/./node_modules/gtoken/build/src/index.js":
/*!************************************************!*\
  !*** ./node_modules/gtoken/build/src/index.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/**\n * Copyright 2018 Google LLC\n *\n * Distributed under MIT license.\n * See file LICENSE for detail or copy at https://opensource.org/licenses/MIT\n */\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar _GoogleToken_instances, _GoogleToken_inFlightRequest, _GoogleToken_getTokenAsync, _GoogleToken_getTokenAsyncInner, _GoogleToken_ensureEmail, _GoogleToken_revokeTokenAsync, _GoogleToken_configure, _GoogleToken_requestToken;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GoogleToken = void 0;\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/src/index.js\");\nconst jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nconst path = __webpack_require__(/*! path */ \"path\");\nconst util_1 = __webpack_require__(/*! util */ \"util\");\nconst readFile = fs.readFile\n    ? (0, util_1.promisify)(fs.readFile)\n    : async () => {\n        // if running in the web-browser, fs.readFile may not have been shimmed.\n        throw new ErrorWithCode('use key rather than keyFile.', 'MISSING_CREDENTIALS');\n    };\nconst GOOGLE_TOKEN_URL = 'https://www.googleapis.com/oauth2/v4/token';\nconst GOOGLE_REVOKE_TOKEN_URL = 'https://accounts.google.com/o/oauth2/revoke?token=';\nclass ErrorWithCode extends Error {\n    constructor(message, code) {\n        super(message);\n        this.code = code;\n    }\n}\nclass GoogleToken {\n    get accessToken() {\n        return this.rawToken ? this.rawToken.access_token : undefined;\n    }\n    get idToken() {\n        return this.rawToken ? this.rawToken.id_token : undefined;\n    }\n    get tokenType() {\n        return this.rawToken ? this.rawToken.token_type : undefined;\n    }\n    get refreshToken() {\n        return this.rawToken ? this.rawToken.refresh_token : undefined;\n    }\n    /**\n     * Create a GoogleToken.\n     *\n     * @param options  Configuration object.\n     */\n    constructor(options) {\n        _GoogleToken_instances.add(this);\n        this.transporter = {\n            request: opts => (0, gaxios_1.request)(opts),\n        };\n        _GoogleToken_inFlightRequest.set(this, void 0);\n        __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_configure).call(this, options);\n    }\n    /**\n     * Returns whether the token has expired.\n     *\n     * @return true if the token has expired, false otherwise.\n     */\n    hasExpired() {\n        const now = new Date().getTime();\n        if (this.rawToken && this.expiresAt) {\n            return now >= this.expiresAt;\n        }\n        else {\n            return true;\n        }\n    }\n    /**\n     * Returns whether the token will expire within eagerRefreshThresholdMillis\n     *\n     * @return true if the token will be expired within eagerRefreshThresholdMillis, false otherwise.\n     */\n    isTokenExpiring() {\n        var _a;\n        const now = new Date().getTime();\n        const eagerRefreshThresholdMillis = (_a = this.eagerRefreshThresholdMillis) !== null && _a !== void 0 ? _a : 0;\n        if (this.rawToken && this.expiresAt) {\n            return this.expiresAt <= now + eagerRefreshThresholdMillis;\n        }\n        else {\n            return true;\n        }\n    }\n    getToken(callback, opts = {}) {\n        if (typeof callback === 'object') {\n            opts = callback;\n            callback = undefined;\n        }\n        opts = Object.assign({\n            forceRefresh: false,\n        }, opts);\n        if (callback) {\n            const cb = callback;\n            __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_getTokenAsync).call(this, opts).then(t => cb(null, t), callback);\n            return;\n        }\n        return __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_getTokenAsync).call(this, opts);\n    }\n    /**\n     * Given a keyFile, extract the key and client email if available\n     * @param keyFile Path to a json, pem, or p12 file that contains the key.\n     * @returns an object with privateKey and clientEmail properties\n     */\n    async getCredentials(keyFile) {\n        const ext = path.extname(keyFile);\n        switch (ext) {\n            case '.json': {\n                const key = await readFile(keyFile, 'utf8');\n                const body = JSON.parse(key);\n                const privateKey = body.private_key;\n                const clientEmail = body.client_email;\n                if (!privateKey || !clientEmail) {\n                    throw new ErrorWithCode('private_key and client_email are required.', 'MISSING_CREDENTIALS');\n                }\n                return { privateKey, clientEmail };\n            }\n            case '.der':\n            case '.crt':\n            case '.pem': {\n                const privateKey = await readFile(keyFile, 'utf8');\n                return { privateKey };\n            }\n            case '.p12':\n            case '.pfx': {\n                throw new ErrorWithCode('*.p12 certificates are not supported after v6.1.2. ' +\n                    'Consider utilizing *.json format or converting *.p12 to *.pem using the OpenSSL CLI.', 'UNKNOWN_CERTIFICATE_TYPE');\n            }\n            default:\n                throw new ErrorWithCode('Unknown certificate type. Type is determined based on file extension. ' +\n                    'Current supported extensions are *.json, and *.pem.', 'UNKNOWN_CERTIFICATE_TYPE');\n        }\n    }\n    revokeToken(callback) {\n        if (callback) {\n            __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_revokeTokenAsync).call(this).then(() => callback(), callback);\n            return;\n        }\n        return __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_revokeTokenAsync).call(this);\n    }\n}\nexports.GoogleToken = GoogleToken;\n_GoogleToken_inFlightRequest = new WeakMap(), _GoogleToken_instances = new WeakSet(), _GoogleToken_getTokenAsync = async function _GoogleToken_getTokenAsync(opts) {\n    if (__classPrivateFieldGet(this, _GoogleToken_inFlightRequest, \"f\") && !opts.forceRefresh) {\n        return __classPrivateFieldGet(this, _GoogleToken_inFlightRequest, \"f\");\n    }\n    try {\n        return await (__classPrivateFieldSet(this, _GoogleToken_inFlightRequest, __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_getTokenAsyncInner).call(this, opts), \"f\"));\n    }\n    finally {\n        __classPrivateFieldSet(this, _GoogleToken_inFlightRequest, undefined, \"f\");\n    }\n}, _GoogleToken_getTokenAsyncInner = async function _GoogleToken_getTokenAsyncInner(opts) {\n    if (this.isTokenExpiring() === false && opts.forceRefresh === false) {\n        return Promise.resolve(this.rawToken);\n    }\n    if (!this.key && !this.keyFile) {\n        throw new Error('No key or keyFile set.');\n    }\n    if (!this.key && this.keyFile) {\n        const creds = await this.getCredentials(this.keyFile);\n        this.key = creds.privateKey;\n        this.iss = creds.clientEmail || this.iss;\n        if (!creds.clientEmail) {\n            __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_ensureEmail).call(this);\n        }\n    }\n    return __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_requestToken).call(this);\n}, _GoogleToken_ensureEmail = function _GoogleToken_ensureEmail() {\n    if (!this.iss) {\n        throw new ErrorWithCode('email is required.', 'MISSING_CREDENTIALS');\n    }\n}, _GoogleToken_revokeTokenAsync = async function _GoogleToken_revokeTokenAsync() {\n    if (!this.accessToken) {\n        throw new Error('No token to revoke.');\n    }\n    const url = GOOGLE_REVOKE_TOKEN_URL + this.accessToken;\n    await this.transporter.request({\n        url,\n        retry: true,\n    });\n    __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_configure).call(this, {\n        email: this.iss,\n        sub: this.sub,\n        key: this.key,\n        keyFile: this.keyFile,\n        scope: this.scope,\n        additionalClaims: this.additionalClaims,\n    });\n}, _GoogleToken_configure = function _GoogleToken_configure(options = {}) {\n    this.keyFile = options.keyFile;\n    this.key = options.key;\n    this.rawToken = undefined;\n    this.iss = options.email || options.iss;\n    this.sub = options.sub;\n    this.additionalClaims = options.additionalClaims;\n    if (typeof options.scope === 'object') {\n        this.scope = options.scope.join(' ');\n    }\n    else {\n        this.scope = options.scope;\n    }\n    this.eagerRefreshThresholdMillis = options.eagerRefreshThresholdMillis;\n    if (options.transporter) {\n        this.transporter = options.transporter;\n    }\n}, _GoogleToken_requestToken = \n/**\n * Request the token from Google.\n */\nasync function _GoogleToken_requestToken() {\n    var _a, _b;\n    const iat = Math.floor(new Date().getTime() / 1000);\n    const additionalClaims = this.additionalClaims || {};\n    const payload = Object.assign({\n        iss: this.iss,\n        scope: this.scope,\n        aud: GOOGLE_TOKEN_URL,\n        exp: iat + 3600,\n        iat,\n        sub: this.sub,\n    }, additionalClaims);\n    const signedJWT = jws.sign({\n        header: { alg: 'RS256' },\n        payload,\n        secret: this.key,\n    });\n    try {\n        const r = await this.transporter.request({\n            method: 'POST',\n            url: GOOGLE_TOKEN_URL,\n            data: {\n                grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',\n                assertion: signedJWT,\n            },\n            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },\n            responseType: 'json',\n            retryConfig: {\n                httpMethodsToRetry: ['POST'],\n            },\n        });\n        this.rawToken = r.data;\n        this.expiresAt =\n            r.data.expires_in === null || r.data.expires_in === undefined\n                ? undefined\n                : (iat + r.data.expires_in) * 1000;\n        return this.rawToken;\n    }\n    catch (e) {\n        this.rawToken = undefined;\n        this.tokenExpires = undefined;\n        const body = e.response && ((_a = e.response) === null || _a === void 0 ? void 0 : _a.data)\n            ? (_b = e.response) === null || _b === void 0 ? void 0 : _b.data\n            : {};\n        if (body.error) {\n            const desc = body.error_description\n                ? `: ${body.error_description}`\n                : '';\n            e.message = `${body.error}${desc}`;\n        }\n        throw e;\n    }\n};\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gtoken/build/src/index.js\n");

/***/ })

};
;