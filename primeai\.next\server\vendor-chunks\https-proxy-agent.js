"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/https-proxy-agent";
exports.ids = ["vendor-chunks/https-proxy-agent"];
exports.modules = {

/***/ "(rsc)/./node_modules/https-proxy-agent/dist/index.js":
/*!******************************************************!*\
  !*** ./node_modules/https-proxy-agent/dist/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.HttpsProxyAgent = void 0;\nconst net = __importStar(__webpack_require__(/*! net */ \"net\"));\nconst tls = __importStar(__webpack_require__(/*! tls */ \"tls\"));\nconst assert_1 = __importDefault(__webpack_require__(/*! assert */ \"assert\"));\nconst debug_1 = __importDefault(__webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\"));\nconst agent_base_1 = __webpack_require__(/*! agent-base */ \"(rsc)/./node_modules/agent-base/dist/index.js\");\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst parse_proxy_response_1 = __webpack_require__(/*! ./parse-proxy-response */ \"(rsc)/./node_modules/https-proxy-agent/dist/parse-proxy-response.js\");\nconst debug = (0, debug_1.default)('https-proxy-agent');\nconst setServernameFromNonIpHost = (options) => {\n    if (options.servername === undefined &&\n        options.host &&\n        !net.isIP(options.host)) {\n        return {\n            ...options,\n            servername: options.host,\n        };\n    }\n    return options;\n};\n/**\n * The `HttpsProxyAgent` implements an HTTP Agent subclass that connects to\n * the specified \"HTTP(s) proxy server\" in order to proxy HTTPS requests.\n *\n * Outgoing HTTP requests are first tunneled through the proxy server using the\n * `CONNECT` HTTP request method to establish a connection to the proxy server,\n * and then the proxy server connects to the destination target and issues the\n * HTTP request from the proxy server.\n *\n * `https:` requests have their socket connection upgraded to TLS once\n * the connection to the proxy server has been established.\n */\nclass HttpsProxyAgent extends agent_base_1.Agent {\n    constructor(proxy, opts) {\n        super(opts);\n        this.options = { path: undefined };\n        this.proxy = typeof proxy === 'string' ? new url_1.URL(proxy) : proxy;\n        this.proxyHeaders = opts?.headers ?? {};\n        debug('Creating new HttpsProxyAgent instance: %o', this.proxy.href);\n        // Trim off the brackets from IPv6 addresses\n        const host = (this.proxy.hostname || this.proxy.host).replace(/^\\[|\\]$/g, '');\n        const port = this.proxy.port\n            ? parseInt(this.proxy.port, 10)\n            : this.proxy.protocol === 'https:'\n                ? 443\n                : 80;\n        this.connectOpts = {\n            // Attempt to negotiate http/1.1 for proxy servers that support http/2\n            ALPNProtocols: ['http/1.1'],\n            ...(opts ? omit(opts, 'headers') : null),\n            host,\n            port,\n        };\n    }\n    /**\n     * Called when the node-core HTTP client library is creating a\n     * new HTTP request.\n     */\n    async connect(req, opts) {\n        const { proxy } = this;\n        if (!opts.host) {\n            throw new TypeError('No \"host\" provided');\n        }\n        // Create a socket connection to the proxy server.\n        let socket;\n        if (proxy.protocol === 'https:') {\n            debug('Creating `tls.Socket`: %o', this.connectOpts);\n            socket = tls.connect(setServernameFromNonIpHost(this.connectOpts));\n        }\n        else {\n            debug('Creating `net.Socket`: %o', this.connectOpts);\n            socket = net.connect(this.connectOpts);\n        }\n        const headers = typeof this.proxyHeaders === 'function'\n            ? this.proxyHeaders()\n            : { ...this.proxyHeaders };\n        const host = net.isIPv6(opts.host) ? `[${opts.host}]` : opts.host;\n        let payload = `CONNECT ${host}:${opts.port} HTTP/1.1\\r\\n`;\n        // Inject the `Proxy-Authorization` header if necessary.\n        if (proxy.username || proxy.password) {\n            const auth = `${decodeURIComponent(proxy.username)}:${decodeURIComponent(proxy.password)}`;\n            headers['Proxy-Authorization'] = `Basic ${Buffer.from(auth).toString('base64')}`;\n        }\n        headers.Host = `${host}:${opts.port}`;\n        if (!headers['Proxy-Connection']) {\n            headers['Proxy-Connection'] = this.keepAlive\n                ? 'Keep-Alive'\n                : 'close';\n        }\n        for (const name of Object.keys(headers)) {\n            payload += `${name}: ${headers[name]}\\r\\n`;\n        }\n        const proxyResponsePromise = (0, parse_proxy_response_1.parseProxyResponse)(socket);\n        socket.write(`${payload}\\r\\n`);\n        const { connect, buffered } = await proxyResponsePromise;\n        req.emit('proxyConnect', connect);\n        this.emit('proxyConnect', connect, req);\n        if (connect.statusCode === 200) {\n            req.once('socket', resume);\n            if (opts.secureEndpoint) {\n                // The proxy is connecting to a TLS server, so upgrade\n                // this socket connection to a TLS connection.\n                debug('Upgrading socket connection to TLS');\n                return tls.connect({\n                    ...omit(setServernameFromNonIpHost(opts), 'host', 'path', 'port'),\n                    socket,\n                });\n            }\n            return socket;\n        }\n        // Some other status code that's not 200... need to re-play the HTTP\n        // header \"data\" events onto the socket once the HTTP machinery is\n        // attached so that the node core `http` can parse and handle the\n        // error status code.\n        // Close the original socket, and a new \"fake\" socket is returned\n        // instead, so that the proxy doesn't get the HTTP request\n        // written to it (which may contain `Authorization` headers or other\n        // sensitive data).\n        //\n        // See: https://hackerone.com/reports/541502\n        socket.destroy();\n        const fakeSocket = new net.Socket({ writable: false });\n        fakeSocket.readable = true;\n        // Need to wait for the \"socket\" event to re-play the \"data\" events.\n        req.once('socket', (s) => {\n            debug('Replaying proxy buffer for failed request');\n            (0, assert_1.default)(s.listenerCount('data') > 0);\n            // Replay the \"buffered\" Buffer onto the fake `socket`, since at\n            // this point the HTTP module machinery has been hooked up for\n            // the user.\n            s.push(buffered);\n            s.push(null);\n        });\n        return fakeSocket;\n    }\n}\nHttpsProxyAgent.protocols = ['http', 'https'];\nexports.HttpsProxyAgent = HttpsProxyAgent;\nfunction resume(socket) {\n    socket.resume();\n}\nfunction omit(obj, ...keys) {\n    const ret = {};\n    let key;\n    for (key in obj) {\n        if (!keys.includes(key)) {\n            ret[key] = obj[key];\n        }\n    }\n    return ret;\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/https-proxy-agent/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/https-proxy-agent/dist/parse-proxy-response.js":
/*!*********************************************************************!*\
  !*** ./node_modules/https-proxy-agent/dist/parse-proxy-response.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseProxyResponse = void 0;\nconst debug_1 = __importDefault(__webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\"));\nconst debug = (0, debug_1.default)('https-proxy-agent:parse-proxy-response');\nfunction parseProxyResponse(socket) {\n    return new Promise((resolve, reject) => {\n        // we need to buffer any HTTP traffic that happens with the proxy before we get\n        // the CONNECT response, so that if the response is anything other than an \"200\"\n        // response code, then we can re-play the \"data\" events on the socket once the\n        // HTTP parser is hooked up...\n        let buffersLength = 0;\n        const buffers = [];\n        function read() {\n            const b = socket.read();\n            if (b)\n                ondata(b);\n            else\n                socket.once('readable', read);\n        }\n        function cleanup() {\n            socket.removeListener('end', onend);\n            socket.removeListener('error', onerror);\n            socket.removeListener('readable', read);\n        }\n        function onend() {\n            cleanup();\n            debug('onend');\n            reject(new Error('Proxy connection ended before receiving CONNECT response'));\n        }\n        function onerror(err) {\n            cleanup();\n            debug('onerror %o', err);\n            reject(err);\n        }\n        function ondata(b) {\n            buffers.push(b);\n            buffersLength += b.length;\n            const buffered = Buffer.concat(buffers, buffersLength);\n            const endOfHeaders = buffered.indexOf('\\r\\n\\r\\n');\n            if (endOfHeaders === -1) {\n                // keep buffering\n                debug('have not received end of HTTP headers yet...');\n                read();\n                return;\n            }\n            const headerParts = buffered\n                .slice(0, endOfHeaders)\n                .toString('ascii')\n                .split('\\r\\n');\n            const firstLine = headerParts.shift();\n            if (!firstLine) {\n                socket.destroy();\n                return reject(new Error('No header received from proxy CONNECT response'));\n            }\n            const firstLineParts = firstLine.split(' ');\n            const statusCode = +firstLineParts[1];\n            const statusText = firstLineParts.slice(2).join(' ');\n            const headers = {};\n            for (const header of headerParts) {\n                if (!header)\n                    continue;\n                const firstColon = header.indexOf(':');\n                if (firstColon === -1) {\n                    socket.destroy();\n                    return reject(new Error(`Invalid header from proxy CONNECT response: \"${header}\"`));\n                }\n                const key = header.slice(0, firstColon).toLowerCase();\n                const value = header.slice(firstColon + 1).trimStart();\n                const current = headers[key];\n                if (typeof current === 'string') {\n                    headers[key] = [current, value];\n                }\n                else if (Array.isArray(current)) {\n                    current.push(value);\n                }\n                else {\n                    headers[key] = value;\n                }\n            }\n            debug('got proxy server response: %o %o', firstLine, headers);\n            cleanup();\n            resolve({\n                connect: {\n                    statusCode,\n                    statusText,\n                    headers,\n                },\n                buffered,\n            });\n        }\n        socket.on('error', onerror);\n        socket.on('end', onend);\n        read();\n    });\n}\nexports.parseProxyResponse = parseProxyResponse;\n//# sourceMappingURL=parse-proxy-response.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/https-proxy-agent/dist/parse-proxy-response.js\n");

/***/ })

};
;