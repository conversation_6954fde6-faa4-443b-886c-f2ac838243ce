"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/marked";
exports.ids = ["vendor-chunks/marked"];
exports.modules = {

/***/ "(ssr)/./node_modules/marked/lib/marked.esm.js":
/*!***********************************************!*\
  !*** ./node_modules/marked/lib/marked.esm.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hooks: () => (/* binding */ _Hooks),\n/* harmony export */   Lexer: () => (/* binding */ _Lexer),\n/* harmony export */   Marked: () => (/* binding */ Marked),\n/* harmony export */   Parser: () => (/* binding */ _Parser),\n/* harmony export */   Renderer: () => (/* binding */ _Renderer),\n/* harmony export */   TextRenderer: () => (/* binding */ _TextRenderer),\n/* harmony export */   Tokenizer: () => (/* binding */ _Tokenizer),\n/* harmony export */   defaults: () => (/* binding */ _defaults),\n/* harmony export */   getDefaults: () => (/* binding */ _getDefaults),\n/* harmony export */   lexer: () => (/* binding */ lexer),\n/* harmony export */   marked: () => (/* binding */ marked),\n/* harmony export */   options: () => (/* binding */ options),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parseInline: () => (/* binding */ parseInline),\n/* harmony export */   parser: () => (/* binding */ parser),\n/* harmony export */   setOptions: () => (/* binding */ setOptions),\n/* harmony export */   use: () => (/* binding */ use),\n/* harmony export */   walkTokens: () => (/* binding */ walkTokens)\n/* harmony export */ });\n/**\n * marked v15.0.12 - a markdown parser\n * Copyright (c) 2011-2025, Christopher Jeffrey. (MIT Licensed)\n * https://github.com/markedjs/marked\n */\n\n/**\n * DO NOT EDIT THIS FILE\n * The code in this file is generated from files in ./src/\n */\n\n\n// src/defaults.ts\nfunction _getDefaults() {\n  return {\n    async: false,\n    breaks: false,\n    extensions: null,\n    gfm: true,\n    hooks: null,\n    pedantic: false,\n    renderer: null,\n    silent: false,\n    tokenizer: null,\n    walkTokens: null\n  };\n}\nvar _defaults = _getDefaults();\nfunction changeDefaults(newDefaults) {\n  _defaults = newDefaults;\n}\n\n// src/rules.ts\nvar noopTest = { exec: () => null };\nfunction edit(regex, opt = \"\") {\n  let source = typeof regex === \"string\" ? regex : regex.source;\n  const obj = {\n    replace: (name, val) => {\n      let valSource = typeof val === \"string\" ? val : val.source;\n      valSource = valSource.replace(other.caret, \"$1\");\n      source = source.replace(name, valSource);\n      return obj;\n    },\n    getRegex: () => {\n      return new RegExp(source, opt);\n    }\n  };\n  return obj;\n}\nvar other = {\n  codeRemoveIndent: /^(?: {1,4}| {0,3}\\t)/gm,\n  outputLinkReplace: /\\\\([\\[\\]])/g,\n  indentCodeCompensation: /^(\\s+)(?:```)/,\n  beginningSpace: /^\\s+/,\n  endingHash: /#$/,\n  startingSpaceChar: /^ /,\n  endingSpaceChar: / $/,\n  nonSpaceChar: /[^ ]/,\n  newLineCharGlobal: /\\n/g,\n  tabCharGlobal: /\\t/g,\n  multipleSpaceGlobal: /\\s+/g,\n  blankLine: /^[ \\t]*$/,\n  doubleBlankLine: /\\n[ \\t]*\\n[ \\t]*$/,\n  blockquoteStart: /^ {0,3}>/,\n  blockquoteSetextReplace: /\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g,\n  blockquoteSetextReplace2: /^ {0,3}>[ \\t]?/gm,\n  listReplaceTabs: /^\\t+/,\n  listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,\n  listIsTask: /^\\[[ xX]\\] /,\n  listReplaceTask: /^\\[[ xX]\\] +/,\n  anyLine: /\\n.*\\n/,\n  hrefBrackets: /^<(.*)>$/,\n  tableDelimiter: /[:|]/,\n  tableAlignChars: /^\\||\\| *$/g,\n  tableRowBlankLine: /\\n[ \\t]*$/,\n  tableAlignRight: /^ *-+: *$/,\n  tableAlignCenter: /^ *:-+: *$/,\n  tableAlignLeft: /^ *:-+ *$/,\n  startATag: /^<a /i,\n  endATag: /^<\\/a>/i,\n  startPreScriptTag: /^<(pre|code|kbd|script)(\\s|>)/i,\n  endPreScriptTag: /^<\\/(pre|code|kbd|script)(\\s|>)/i,\n  startAngleBracket: /^</,\n  endAngleBracket: />$/,\n  pedanticHrefTitle: /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/,\n  unicodeAlphaNumeric: /[\\p{L}\\p{N}]/u,\n  escapeTest: /[&<>\"']/,\n  escapeReplace: /[&<>\"']/g,\n  escapeTestNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/,\n  escapeReplaceNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/g,\n  unescapeTest: /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig,\n  caret: /(^|[^\\[])\\^/g,\n  percentDecode: /%25/g,\n  findPipe: /\\|/g,\n  splitPipe: / \\|/,\n  slashPipe: /\\\\\\|/g,\n  carriageReturn: /\\r\\n|\\r/g,\n  spaceLine: /^ +$/gm,\n  notSpaceStart: /^\\S*/,\n  endingNewline: /\\n$/,\n  listItemRegex: (bull) => new RegExp(`^( {0,3}${bull})((?:[\t ][^\\\\n]*)?(?:\\\\n|$))`),\n  nextBulletRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \t][^\\\\n]*)?(?:\\\\n|$))`),\n  hrRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`),\n  fencesBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`),\n  headingBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`),\n  htmlBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}<(?:[a-z].*>|!--)`, \"i\")\n};\nvar newline = /^(?:[ \\t]*(?:\\n|$))+/;\nvar blockCode = /^((?: {4}| {0,3}\\t)[^\\n]+(?:\\n(?:[ \\t]*(?:\\n|$))*)?)+/;\nvar fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\nvar hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\nvar heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\nvar bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nvar lheadingCore = /^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/;\nvar lheading = edit(lheadingCore).replace(/bull/g, bullet).replace(/blockCode/g, /(?: {4}| {0,3}\\t)/).replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g, / {0,3}>/).replace(/heading/g, / {0,3}#{1,6}/).replace(/html/g, / {0,3}<[^\\n>]+>\\n/).replace(/\\|table/g, \"\").getRegex();\nvar lheadingGfm = edit(lheadingCore).replace(/bull/g, bullet).replace(/blockCode/g, /(?: {4}| {0,3}\\t)/).replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g, / {0,3}>/).replace(/heading/g, / {0,3}#{1,6}/).replace(/html/g, / {0,3}<[^\\n>]+>\\n/).replace(/table/g, / {0,3}\\|?(?:[:\\- ]*\\|)+[\\:\\- ]*\\n/).getRegex();\nvar _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\nvar blockText = /^[^\\n]+/;\nvar _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nvar def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n[ \\t]*)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n[ \\t]*)?| *\\n[ \\t]*)(title))? *(?:\\n+|$)/).replace(\"label\", _blockLabel).replace(\"title\", /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/).getRegex();\nvar list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/).replace(/bull/g, bullet).getRegex();\nvar _tag = \"address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul\";\nvar _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\nvar html = edit(\n  \"^ {0,3}(?:<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)|comment[^\\\\n]*(\\\\n+|$)|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$)|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$)|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$))\",\n  \"i\"\n).replace(\"comment\", _comment).replace(\"tag\", _tag).replace(\"attribute\", / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/).getRegex();\nvar paragraph = edit(_paragraph).replace(\"hr\", hr).replace(\"heading\", \" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"|lheading\", \"\").replace(\"|table\", \"\").replace(\"blockquote\", \" {0,3}>\").replace(\"fences\", \" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\", \" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\", \"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\", _tag).getRegex();\nvar blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/).replace(\"paragraph\", paragraph).getRegex();\nvar blockNormal = {\n  blockquote,\n  code: blockCode,\n  def,\n  fences,\n  heading,\n  hr,\n  html,\n  lheading,\n  list,\n  newline,\n  paragraph,\n  table: noopTest,\n  text: blockText\n};\nvar gfmTable = edit(\n  \"^ *([^\\\\n ].*)\\\\n {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)\"\n).replace(\"hr\", hr).replace(\"heading\", \" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"blockquote\", \" {0,3}>\").replace(\"code\", \"(?: {4}| {0,3}\t)[^\\\\n]\").replace(\"fences\", \" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\", \" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\", \"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\", _tag).getRegex();\nvar blockGfm = {\n  ...blockNormal,\n  lheading: lheadingGfm,\n  table: gfmTable,\n  paragraph: edit(_paragraph).replace(\"hr\", hr).replace(\"heading\", \" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"|lheading\", \"\").replace(\"table\", gfmTable).replace(\"blockquote\", \" {0,3}>\").replace(\"fences\", \" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\", \" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\", \"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\", _tag).getRegex()\n};\nvar blockPedantic = {\n  ...blockNormal,\n  html: edit(\n    `^ *(?:comment *(?:\\\\n|\\\\s*$)|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\\\s[^'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))`\n  ).replace(\"comment\", _comment).replace(/tag/g, \"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b\").getRegex(),\n  def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n  heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n  fences: noopTest,\n  // fences not supported\n  lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n  paragraph: edit(_paragraph).replace(\"hr\", hr).replace(\"heading\", \" *#{1,6} *[^\\n]\").replace(\"lheading\", lheading).replace(\"|table\", \"\").replace(\"blockquote\", \" {0,3}>\").replace(\"|fences\", \"\").replace(\"|list\", \"\").replace(\"|html\", \"\").replace(\"|tag\", \"\").getRegex()\n};\nvar escape = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\nvar inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\nvar br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\nvar inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\nvar _punctuation = /[\\p{P}\\p{S}]/u;\nvar _punctuationOrSpace = /[\\s\\p{P}\\p{S}]/u;\nvar _notPunctuationOrSpace = /[^\\s\\p{P}\\p{S}]/u;\nvar punctuation = edit(/^((?![*_])punctSpace)/, \"u\").replace(/punctSpace/g, _punctuationOrSpace).getRegex();\nvar _punctuationGfmStrongEm = /(?!~)[\\p{P}\\p{S}]/u;\nvar _punctuationOrSpaceGfmStrongEm = /(?!~)[\\s\\p{P}\\p{S}]/u;\nvar _notPunctuationOrSpaceGfmStrongEm = /(?:[^\\s\\p{P}\\p{S}]|~)/u;\nvar blockSkip = /\\[[^[\\]]*?\\]\\((?:\\\\.|[^\\\\\\(\\)]|\\((?:\\\\.|[^\\\\\\(\\)])*\\))*\\)|`[^`]*?`|<[^<>]*?>/g;\nvar emStrongLDelimCore = /^(?:\\*+(?:((?!\\*)punct)|[^\\s*]))|^_+(?:((?!_)punct)|([^\\s_]))/;\nvar emStrongLDelim = edit(emStrongLDelimCore, \"u\").replace(/punct/g, _punctuation).getRegex();\nvar emStrongLDelimGfm = edit(emStrongLDelimCore, \"u\").replace(/punct/g, _punctuationGfmStrongEm).getRegex();\nvar emStrongRDelimAstCore = \"^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\\\*)punct(\\\\*+)(?=[\\\\s]|$)|notPunctSpace(\\\\*+)(?!\\\\*)(?=punctSpace|$)|(?!\\\\*)punctSpace(\\\\*+)(?=notPunctSpace)|[\\\\s](\\\\*+)(?!\\\\*)(?=punct)|(?!\\\\*)punct(\\\\*+)(?!\\\\*)(?=punct)|notPunctSpace(\\\\*+)(?=notPunctSpace)\";\nvar emStrongRDelimAst = edit(emStrongRDelimAstCore, \"gu\").replace(/notPunctSpace/g, _notPunctuationOrSpace).replace(/punctSpace/g, _punctuationOrSpace).replace(/punct/g, _punctuation).getRegex();\nvar emStrongRDelimAstGfm = edit(emStrongRDelimAstCore, \"gu\").replace(/notPunctSpace/g, _notPunctuationOrSpaceGfmStrongEm).replace(/punctSpace/g, _punctuationOrSpaceGfmStrongEm).replace(/punct/g, _punctuationGfmStrongEm).getRegex();\nvar emStrongRDelimUnd = edit(\n  \"^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)\",\n  \"gu\"\n).replace(/notPunctSpace/g, _notPunctuationOrSpace).replace(/punctSpace/g, _punctuationOrSpace).replace(/punct/g, _punctuation).getRegex();\nvar anyPunctuation = edit(/\\\\(punct)/, \"gu\").replace(/punct/g, _punctuation).getRegex();\nvar autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/).replace(\"scheme\", /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace(\"email\", /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex();\nvar _inlineComment = edit(_comment).replace(\"(?:-->|$)\", \"-->\").getRegex();\nvar tag = edit(\n  \"^comment|^</[a-zA-Z][\\\\w:-]*\\\\s*>|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>|^<\\\\?[\\\\s\\\\S]*?\\\\?>|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>\"\n).replace(\"comment\", _inlineComment).replace(\"attribute\", /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/).getRegex();\nvar _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\nvar link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:(?:[ \\t]*(?:\\n[ \\t]*)?)(title))?\\s*\\)/).replace(\"label\", _inlineLabel).replace(\"href\", /<(?:\\\\.|[^\\n<>\\\\])+>|[^ \\t\\n\\x00-\\x1f]*/).replace(\"title\", /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/).getRegex();\nvar reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/).replace(\"label\", _inlineLabel).replace(\"ref\", _blockLabel).getRegex();\nvar nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/).replace(\"ref\", _blockLabel).getRegex();\nvar reflinkSearch = edit(\"reflink|nolink(?!\\\\()\", \"g\").replace(\"reflink\", reflink).replace(\"nolink\", nolink).getRegex();\nvar inlineNormal = {\n  _backpedal: noopTest,\n  // only used for GFM url\n  anyPunctuation,\n  autolink,\n  blockSkip,\n  br,\n  code: inlineCode,\n  del: noopTest,\n  emStrongLDelim,\n  emStrongRDelimAst,\n  emStrongRDelimUnd,\n  escape,\n  link,\n  nolink,\n  punctuation,\n  reflink,\n  reflinkSearch,\n  tag,\n  text: inlineText,\n  url: noopTest\n};\nvar inlinePedantic = {\n  ...inlineNormal,\n  link: edit(/^!?\\[(label)\\]\\((.*?)\\)/).replace(\"label\", _inlineLabel).getRegex(),\n  reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/).replace(\"label\", _inlineLabel).getRegex()\n};\nvar inlineGfm = {\n  ...inlineNormal,\n  emStrongRDelimAst: emStrongRDelimAstGfm,\n  emStrongLDelim: emStrongLDelimGfm,\n  url: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, \"i\").replace(\"email\", /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),\n  _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n  del: /^(~~?)(?=[^\\s~])((?:\\\\.|[^\\\\])*?(?:\\\\.|[^\\s~\\\\]))\\1(?=[^~]|$)/,\n  text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/\n};\nvar inlineBreaks = {\n  ...inlineGfm,\n  br: edit(br).replace(\"{2,}\", \"*\").getRegex(),\n  text: edit(inlineGfm.text).replace(\"\\\\b_\", \"\\\\b_| {2,}\\\\n\").replace(/\\{2,\\}/g, \"*\").getRegex()\n};\nvar block = {\n  normal: blockNormal,\n  gfm: blockGfm,\n  pedantic: blockPedantic\n};\nvar inline = {\n  normal: inlineNormal,\n  gfm: inlineGfm,\n  breaks: inlineBreaks,\n  pedantic: inlinePedantic\n};\n\n// src/helpers.ts\nvar escapeReplacements = {\n  \"&\": \"&amp;\",\n  \"<\": \"&lt;\",\n  \">\": \"&gt;\",\n  '\"': \"&quot;\",\n  \"'\": \"&#39;\"\n};\nvar getEscapeReplacement = (ch) => escapeReplacements[ch];\nfunction escape2(html2, encode) {\n  if (encode) {\n    if (other.escapeTest.test(html2)) {\n      return html2.replace(other.escapeReplace, getEscapeReplacement);\n    }\n  } else {\n    if (other.escapeTestNoEncode.test(html2)) {\n      return html2.replace(other.escapeReplaceNoEncode, getEscapeReplacement);\n    }\n  }\n  return html2;\n}\nfunction cleanUrl(href) {\n  try {\n    href = encodeURI(href).replace(other.percentDecode, \"%\");\n  } catch {\n    return null;\n  }\n  return href;\n}\nfunction splitCells(tableRow, count) {\n  const row = tableRow.replace(other.findPipe, (match, offset, str) => {\n    let escaped = false;\n    let curr = offset;\n    while (--curr >= 0 && str[curr] === \"\\\\\") escaped = !escaped;\n    if (escaped) {\n      return \"|\";\n    } else {\n      return \" |\";\n    }\n  }), cells = row.split(other.splitPipe);\n  let i = 0;\n  if (!cells[0].trim()) {\n    cells.shift();\n  }\n  if (cells.length > 0 && !cells.at(-1)?.trim()) {\n    cells.pop();\n  }\n  if (count) {\n    if (cells.length > count) {\n      cells.splice(count);\n    } else {\n      while (cells.length < count) cells.push(\"\");\n    }\n  }\n  for (; i < cells.length; i++) {\n    cells[i] = cells[i].trim().replace(other.slashPipe, \"|\");\n  }\n  return cells;\n}\nfunction rtrim(str, c, invert) {\n  const l = str.length;\n  if (l === 0) {\n    return \"\";\n  }\n  let suffLen = 0;\n  while (suffLen < l) {\n    const currChar = str.charAt(l - suffLen - 1);\n    if (currChar === c && !invert) {\n      suffLen++;\n    } else if (currChar !== c && invert) {\n      suffLen++;\n    } else {\n      break;\n    }\n  }\n  return str.slice(0, l - suffLen);\n}\nfunction findClosingBracket(str, b) {\n  if (str.indexOf(b[1]) === -1) {\n    return -1;\n  }\n  let level = 0;\n  for (let i = 0; i < str.length; i++) {\n    if (str[i] === \"\\\\\") {\n      i++;\n    } else if (str[i] === b[0]) {\n      level++;\n    } else if (str[i] === b[1]) {\n      level--;\n      if (level < 0) {\n        return i;\n      }\n    }\n  }\n  if (level > 0) {\n    return -2;\n  }\n  return -1;\n}\n\n// src/Tokenizer.ts\nfunction outputLink(cap, link2, raw, lexer2, rules) {\n  const href = link2.href;\n  const title = link2.title || null;\n  const text = cap[1].replace(rules.other.outputLinkReplace, \"$1\");\n  lexer2.state.inLink = true;\n  const token = {\n    type: cap[0].charAt(0) === \"!\" ? \"image\" : \"link\",\n    raw,\n    href,\n    title,\n    text,\n    tokens: lexer2.inlineTokens(text)\n  };\n  lexer2.state.inLink = false;\n  return token;\n}\nfunction indentCodeCompensation(raw, text, rules) {\n  const matchIndentToCode = raw.match(rules.other.indentCodeCompensation);\n  if (matchIndentToCode === null) {\n    return text;\n  }\n  const indentToCode = matchIndentToCode[1];\n  return text.split(\"\\n\").map((node) => {\n    const matchIndentInNode = node.match(rules.other.beginningSpace);\n    if (matchIndentInNode === null) {\n      return node;\n    }\n    const [indentInNode] = matchIndentInNode;\n    if (indentInNode.length >= indentToCode.length) {\n      return node.slice(indentToCode.length);\n    }\n    return node;\n  }).join(\"\\n\");\n}\nvar _Tokenizer = class {\n  options;\n  rules;\n  // set by the lexer\n  lexer;\n  // set by the lexer\n  constructor(options2) {\n    this.options = options2 || _defaults;\n  }\n  space(src) {\n    const cap = this.rules.block.newline.exec(src);\n    if (cap && cap[0].length > 0) {\n      return {\n        type: \"space\",\n        raw: cap[0]\n      };\n    }\n  }\n  code(src) {\n    const cap = this.rules.block.code.exec(src);\n    if (cap) {\n      const text = cap[0].replace(this.rules.other.codeRemoveIndent, \"\");\n      return {\n        type: \"code\",\n        raw: cap[0],\n        codeBlockStyle: \"indented\",\n        text: !this.options.pedantic ? rtrim(text, \"\\n\") : text\n      };\n    }\n  }\n  fences(src) {\n    const cap = this.rules.block.fences.exec(src);\n    if (cap) {\n      const raw = cap[0];\n      const text = indentCodeCompensation(raw, cap[3] || \"\", this.rules);\n      return {\n        type: \"code\",\n        raw,\n        lang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, \"$1\") : cap[2],\n        text\n      };\n    }\n  }\n  heading(src) {\n    const cap = this.rules.block.heading.exec(src);\n    if (cap) {\n      let text = cap[2].trim();\n      if (this.rules.other.endingHash.test(text)) {\n        const trimmed = rtrim(text, \"#\");\n        if (this.options.pedantic) {\n          text = trimmed.trim();\n        } else if (!trimmed || this.rules.other.endingSpaceChar.test(trimmed)) {\n          text = trimmed.trim();\n        }\n      }\n      return {\n        type: \"heading\",\n        raw: cap[0],\n        depth: cap[1].length,\n        text,\n        tokens: this.lexer.inline(text)\n      };\n    }\n  }\n  hr(src) {\n    const cap = this.rules.block.hr.exec(src);\n    if (cap) {\n      return {\n        type: \"hr\",\n        raw: rtrim(cap[0], \"\\n\")\n      };\n    }\n  }\n  blockquote(src) {\n    const cap = this.rules.block.blockquote.exec(src);\n    if (cap) {\n      let lines = rtrim(cap[0], \"\\n\").split(\"\\n\");\n      let raw = \"\";\n      let text = \"\";\n      const tokens = [];\n      while (lines.length > 0) {\n        let inBlockquote = false;\n        const currentLines = [];\n        let i;\n        for (i = 0; i < lines.length; i++) {\n          if (this.rules.other.blockquoteStart.test(lines[i])) {\n            currentLines.push(lines[i]);\n            inBlockquote = true;\n          } else if (!inBlockquote) {\n            currentLines.push(lines[i]);\n          } else {\n            break;\n          }\n        }\n        lines = lines.slice(i);\n        const currentRaw = currentLines.join(\"\\n\");\n        const currentText = currentRaw.replace(this.rules.other.blockquoteSetextReplace, \"\\n    $1\").replace(this.rules.other.blockquoteSetextReplace2, \"\");\n        raw = raw ? `${raw}\n${currentRaw}` : currentRaw;\n        text = text ? `${text}\n${currentText}` : currentText;\n        const top = this.lexer.state.top;\n        this.lexer.state.top = true;\n        this.lexer.blockTokens(currentText, tokens, true);\n        this.lexer.state.top = top;\n        if (lines.length === 0) {\n          break;\n        }\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"code\") {\n          break;\n        } else if (lastToken?.type === \"blockquote\") {\n          const oldToken = lastToken;\n          const newText = oldToken.raw + \"\\n\" + lines.join(\"\\n\");\n          const newToken = this.blockquote(newText);\n          tokens[tokens.length - 1] = newToken;\n          raw = raw.substring(0, raw.length - oldToken.raw.length) + newToken.raw;\n          text = text.substring(0, text.length - oldToken.text.length) + newToken.text;\n          break;\n        } else if (lastToken?.type === \"list\") {\n          const oldToken = lastToken;\n          const newText = oldToken.raw + \"\\n\" + lines.join(\"\\n\");\n          const newToken = this.list(newText);\n          tokens[tokens.length - 1] = newToken;\n          raw = raw.substring(0, raw.length - lastToken.raw.length) + newToken.raw;\n          text = text.substring(0, text.length - oldToken.raw.length) + newToken.raw;\n          lines = newText.substring(tokens.at(-1).raw.length).split(\"\\n\");\n          continue;\n        }\n      }\n      return {\n        type: \"blockquote\",\n        raw,\n        tokens,\n        text\n      };\n    }\n  }\n  list(src) {\n    let cap = this.rules.block.list.exec(src);\n    if (cap) {\n      let bull = cap[1].trim();\n      const isordered = bull.length > 1;\n      const list2 = {\n        type: \"list\",\n        raw: \"\",\n        ordered: isordered,\n        start: isordered ? +bull.slice(0, -1) : \"\",\n        loose: false,\n        items: []\n      };\n      bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n      if (this.options.pedantic) {\n        bull = isordered ? bull : \"[*+-]\";\n      }\n      const itemRegex = this.rules.other.listItemRegex(bull);\n      let endsWithBlankLine = false;\n      while (src) {\n        let endEarly = false;\n        let raw = \"\";\n        let itemContents = \"\";\n        if (!(cap = itemRegex.exec(src))) {\n          break;\n        }\n        if (this.rules.block.hr.test(src)) {\n          break;\n        }\n        raw = cap[0];\n        src = src.substring(raw.length);\n        let line = cap[2].split(\"\\n\", 1)[0].replace(this.rules.other.listReplaceTabs, (t) => \" \".repeat(3 * t.length));\n        let nextLine = src.split(\"\\n\", 1)[0];\n        let blankLine = !line.trim();\n        let indent = 0;\n        if (this.options.pedantic) {\n          indent = 2;\n          itemContents = line.trimStart();\n        } else if (blankLine) {\n          indent = cap[1].length + 1;\n        } else {\n          indent = cap[2].search(this.rules.other.nonSpaceChar);\n          indent = indent > 4 ? 1 : indent;\n          itemContents = line.slice(indent);\n          indent += cap[1].length;\n        }\n        if (blankLine && this.rules.other.blankLine.test(nextLine)) {\n          raw += nextLine + \"\\n\";\n          src = src.substring(nextLine.length + 1);\n          endEarly = true;\n        }\n        if (!endEarly) {\n          const nextBulletRegex = this.rules.other.nextBulletRegex(indent);\n          const hrRegex = this.rules.other.hrRegex(indent);\n          const fencesBeginRegex = this.rules.other.fencesBeginRegex(indent);\n          const headingBeginRegex = this.rules.other.headingBeginRegex(indent);\n          const htmlBeginRegex = this.rules.other.htmlBeginRegex(indent);\n          while (src) {\n            const rawLine = src.split(\"\\n\", 1)[0];\n            let nextLineWithoutTabs;\n            nextLine = rawLine;\n            if (this.options.pedantic) {\n              nextLine = nextLine.replace(this.rules.other.listReplaceNesting, \"  \");\n              nextLineWithoutTabs = nextLine;\n            } else {\n              nextLineWithoutTabs = nextLine.replace(this.rules.other.tabCharGlobal, \"    \");\n            }\n            if (fencesBeginRegex.test(nextLine)) {\n              break;\n            }\n            if (headingBeginRegex.test(nextLine)) {\n              break;\n            }\n            if (htmlBeginRegex.test(nextLine)) {\n              break;\n            }\n            if (nextBulletRegex.test(nextLine)) {\n              break;\n            }\n            if (hrRegex.test(nextLine)) {\n              break;\n            }\n            if (nextLineWithoutTabs.search(this.rules.other.nonSpaceChar) >= indent || !nextLine.trim()) {\n              itemContents += \"\\n\" + nextLineWithoutTabs.slice(indent);\n            } else {\n              if (blankLine) {\n                break;\n              }\n              if (line.replace(this.rules.other.tabCharGlobal, \"    \").search(this.rules.other.nonSpaceChar) >= 4) {\n                break;\n              }\n              if (fencesBeginRegex.test(line)) {\n                break;\n              }\n              if (headingBeginRegex.test(line)) {\n                break;\n              }\n              if (hrRegex.test(line)) {\n                break;\n              }\n              itemContents += \"\\n\" + nextLine;\n            }\n            if (!blankLine && !nextLine.trim()) {\n              blankLine = true;\n            }\n            raw += rawLine + \"\\n\";\n            src = src.substring(rawLine.length + 1);\n            line = nextLineWithoutTabs.slice(indent);\n          }\n        }\n        if (!list2.loose) {\n          if (endsWithBlankLine) {\n            list2.loose = true;\n          } else if (this.rules.other.doubleBlankLine.test(raw)) {\n            endsWithBlankLine = true;\n          }\n        }\n        let istask = null;\n        let ischecked;\n        if (this.options.gfm) {\n          istask = this.rules.other.listIsTask.exec(itemContents);\n          if (istask) {\n            ischecked = istask[0] !== \"[ ] \";\n            itemContents = itemContents.replace(this.rules.other.listReplaceTask, \"\");\n          }\n        }\n        list2.items.push({\n          type: \"list_item\",\n          raw,\n          task: !!istask,\n          checked: ischecked,\n          loose: false,\n          text: itemContents,\n          tokens: []\n        });\n        list2.raw += raw;\n      }\n      const lastItem = list2.items.at(-1);\n      if (lastItem) {\n        lastItem.raw = lastItem.raw.trimEnd();\n        lastItem.text = lastItem.text.trimEnd();\n      } else {\n        return;\n      }\n      list2.raw = list2.raw.trimEnd();\n      for (let i = 0; i < list2.items.length; i++) {\n        this.lexer.state.top = false;\n        list2.items[i].tokens = this.lexer.blockTokens(list2.items[i].text, []);\n        if (!list2.loose) {\n          const spacers = list2.items[i].tokens.filter((t) => t.type === \"space\");\n          const hasMultipleLineBreaks = spacers.length > 0 && spacers.some((t) => this.rules.other.anyLine.test(t.raw));\n          list2.loose = hasMultipleLineBreaks;\n        }\n      }\n      if (list2.loose) {\n        for (let i = 0; i < list2.items.length; i++) {\n          list2.items[i].loose = true;\n        }\n      }\n      return list2;\n    }\n  }\n  html(src) {\n    const cap = this.rules.block.html.exec(src);\n    if (cap) {\n      const token = {\n        type: \"html\",\n        block: true,\n        raw: cap[0],\n        pre: cap[1] === \"pre\" || cap[1] === \"script\" || cap[1] === \"style\",\n        text: cap[0]\n      };\n      return token;\n    }\n  }\n  def(src) {\n    const cap = this.rules.block.def.exec(src);\n    if (cap) {\n      const tag2 = cap[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal, \" \");\n      const href = cap[2] ? cap[2].replace(this.rules.other.hrefBrackets, \"$1\").replace(this.rules.inline.anyPunctuation, \"$1\") : \"\";\n      const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, \"$1\") : cap[3];\n      return {\n        type: \"def\",\n        tag: tag2,\n        raw: cap[0],\n        href,\n        title\n      };\n    }\n  }\n  table(src) {\n    const cap = this.rules.block.table.exec(src);\n    if (!cap) {\n      return;\n    }\n    if (!this.rules.other.tableDelimiter.test(cap[2])) {\n      return;\n    }\n    const headers = splitCells(cap[1]);\n    const aligns = cap[2].replace(this.rules.other.tableAlignChars, \"\").split(\"|\");\n    const rows = cap[3]?.trim() ? cap[3].replace(this.rules.other.tableRowBlankLine, \"\").split(\"\\n\") : [];\n    const item = {\n      type: \"table\",\n      raw: cap[0],\n      header: [],\n      align: [],\n      rows: []\n    };\n    if (headers.length !== aligns.length) {\n      return;\n    }\n    for (const align of aligns) {\n      if (this.rules.other.tableAlignRight.test(align)) {\n        item.align.push(\"right\");\n      } else if (this.rules.other.tableAlignCenter.test(align)) {\n        item.align.push(\"center\");\n      } else if (this.rules.other.tableAlignLeft.test(align)) {\n        item.align.push(\"left\");\n      } else {\n        item.align.push(null);\n      }\n    }\n    for (let i = 0; i < headers.length; i++) {\n      item.header.push({\n        text: headers[i],\n        tokens: this.lexer.inline(headers[i]),\n        header: true,\n        align: item.align[i]\n      });\n    }\n    for (const row of rows) {\n      item.rows.push(splitCells(row, item.header.length).map((cell, i) => {\n        return {\n          text: cell,\n          tokens: this.lexer.inline(cell),\n          header: false,\n          align: item.align[i]\n        };\n      }));\n    }\n    return item;\n  }\n  lheading(src) {\n    const cap = this.rules.block.lheading.exec(src);\n    if (cap) {\n      return {\n        type: \"heading\",\n        raw: cap[0],\n        depth: cap[2].charAt(0) === \"=\" ? 1 : 2,\n        text: cap[1],\n        tokens: this.lexer.inline(cap[1])\n      };\n    }\n  }\n  paragraph(src) {\n    const cap = this.rules.block.paragraph.exec(src);\n    if (cap) {\n      const text = cap[1].charAt(cap[1].length - 1) === \"\\n\" ? cap[1].slice(0, -1) : cap[1];\n      return {\n        type: \"paragraph\",\n        raw: cap[0],\n        text,\n        tokens: this.lexer.inline(text)\n      };\n    }\n  }\n  text(src) {\n    const cap = this.rules.block.text.exec(src);\n    if (cap) {\n      return {\n        type: \"text\",\n        raw: cap[0],\n        text: cap[0],\n        tokens: this.lexer.inline(cap[0])\n      };\n    }\n  }\n  escape(src) {\n    const cap = this.rules.inline.escape.exec(src);\n    if (cap) {\n      return {\n        type: \"escape\",\n        raw: cap[0],\n        text: cap[1]\n      };\n    }\n  }\n  tag(src) {\n    const cap = this.rules.inline.tag.exec(src);\n    if (cap) {\n      if (!this.lexer.state.inLink && this.rules.other.startATag.test(cap[0])) {\n        this.lexer.state.inLink = true;\n      } else if (this.lexer.state.inLink && this.rules.other.endATag.test(cap[0])) {\n        this.lexer.state.inLink = false;\n      }\n      if (!this.lexer.state.inRawBlock && this.rules.other.startPreScriptTag.test(cap[0])) {\n        this.lexer.state.inRawBlock = true;\n      } else if (this.lexer.state.inRawBlock && this.rules.other.endPreScriptTag.test(cap[0])) {\n        this.lexer.state.inRawBlock = false;\n      }\n      return {\n        type: \"html\",\n        raw: cap[0],\n        inLink: this.lexer.state.inLink,\n        inRawBlock: this.lexer.state.inRawBlock,\n        block: false,\n        text: cap[0]\n      };\n    }\n  }\n  link(src) {\n    const cap = this.rules.inline.link.exec(src);\n    if (cap) {\n      const trimmedUrl = cap[2].trim();\n      if (!this.options.pedantic && this.rules.other.startAngleBracket.test(trimmedUrl)) {\n        if (!this.rules.other.endAngleBracket.test(trimmedUrl)) {\n          return;\n        }\n        const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), \"\\\\\");\n        if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n          return;\n        }\n      } else {\n        const lastParenIndex = findClosingBracket(cap[2], \"()\");\n        if (lastParenIndex === -2) {\n          return;\n        }\n        if (lastParenIndex > -1) {\n          const start = cap[0].indexOf(\"!\") === 0 ? 5 : 4;\n          const linkLen = start + cap[1].length + lastParenIndex;\n          cap[2] = cap[2].substring(0, lastParenIndex);\n          cap[0] = cap[0].substring(0, linkLen).trim();\n          cap[3] = \"\";\n        }\n      }\n      let href = cap[2];\n      let title = \"\";\n      if (this.options.pedantic) {\n        const link2 = this.rules.other.pedanticHrefTitle.exec(href);\n        if (link2) {\n          href = link2[1];\n          title = link2[3];\n        }\n      } else {\n        title = cap[3] ? cap[3].slice(1, -1) : \"\";\n      }\n      href = href.trim();\n      if (this.rules.other.startAngleBracket.test(href)) {\n        if (this.options.pedantic && !this.rules.other.endAngleBracket.test(trimmedUrl)) {\n          href = href.slice(1);\n        } else {\n          href = href.slice(1, -1);\n        }\n      }\n      return outputLink(cap, {\n        href: href ? href.replace(this.rules.inline.anyPunctuation, \"$1\") : href,\n        title: title ? title.replace(this.rules.inline.anyPunctuation, \"$1\") : title\n      }, cap[0], this.lexer, this.rules);\n    }\n  }\n  reflink(src, links) {\n    let cap;\n    if ((cap = this.rules.inline.reflink.exec(src)) || (cap = this.rules.inline.nolink.exec(src))) {\n      const linkString = (cap[2] || cap[1]).replace(this.rules.other.multipleSpaceGlobal, \" \");\n      const link2 = links[linkString.toLowerCase()];\n      if (!link2) {\n        const text = cap[0].charAt(0);\n        return {\n          type: \"text\",\n          raw: text,\n          text\n        };\n      }\n      return outputLink(cap, link2, cap[0], this.lexer, this.rules);\n    }\n  }\n  emStrong(src, maskedSrc, prevChar = \"\") {\n    let match = this.rules.inline.emStrongLDelim.exec(src);\n    if (!match) return;\n    if (match[3] && prevChar.match(this.rules.other.unicodeAlphaNumeric)) return;\n    const nextChar = match[1] || match[2] || \"\";\n    if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n      const lLength = [...match[0]].length - 1;\n      let rDelim, rLength, delimTotal = lLength, midDelimTotal = 0;\n      const endReg = match[0][0] === \"*\" ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n      endReg.lastIndex = 0;\n      maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n      while ((match = endReg.exec(maskedSrc)) != null) {\n        rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n        if (!rDelim) continue;\n        rLength = [...rDelim].length;\n        if (match[3] || match[4]) {\n          delimTotal += rLength;\n          continue;\n        } else if (match[5] || match[6]) {\n          if (lLength % 3 && !((lLength + rLength) % 3)) {\n            midDelimTotal += rLength;\n            continue;\n          }\n        }\n        delimTotal -= rLength;\n        if (delimTotal > 0) continue;\n        rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n        const lastCharLength = [...match[0]][0].length;\n        const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n        if (Math.min(lLength, rLength) % 2) {\n          const text2 = raw.slice(1, -1);\n          return {\n            type: \"em\",\n            raw,\n            text: text2,\n            tokens: this.lexer.inlineTokens(text2)\n          };\n        }\n        const text = raw.slice(2, -2);\n        return {\n          type: \"strong\",\n          raw,\n          text,\n          tokens: this.lexer.inlineTokens(text)\n        };\n      }\n    }\n  }\n  codespan(src) {\n    const cap = this.rules.inline.code.exec(src);\n    if (cap) {\n      let text = cap[2].replace(this.rules.other.newLineCharGlobal, \" \");\n      const hasNonSpaceChars = this.rules.other.nonSpaceChar.test(text);\n      const hasSpaceCharsOnBothEnds = this.rules.other.startingSpaceChar.test(text) && this.rules.other.endingSpaceChar.test(text);\n      if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n        text = text.substring(1, text.length - 1);\n      }\n      return {\n        type: \"codespan\",\n        raw: cap[0],\n        text\n      };\n    }\n  }\n  br(src) {\n    const cap = this.rules.inline.br.exec(src);\n    if (cap) {\n      return {\n        type: \"br\",\n        raw: cap[0]\n      };\n    }\n  }\n  del(src) {\n    const cap = this.rules.inline.del.exec(src);\n    if (cap) {\n      return {\n        type: \"del\",\n        raw: cap[0],\n        text: cap[2],\n        tokens: this.lexer.inlineTokens(cap[2])\n      };\n    }\n  }\n  autolink(src) {\n    const cap = this.rules.inline.autolink.exec(src);\n    if (cap) {\n      let text, href;\n      if (cap[2] === \"@\") {\n        text = cap[1];\n        href = \"mailto:\" + text;\n      } else {\n        text = cap[1];\n        href = text;\n      }\n      return {\n        type: \"link\",\n        raw: cap[0],\n        text,\n        href,\n        tokens: [\n          {\n            type: \"text\",\n            raw: text,\n            text\n          }\n        ]\n      };\n    }\n  }\n  url(src) {\n    let cap;\n    if (cap = this.rules.inline.url.exec(src)) {\n      let text, href;\n      if (cap[2] === \"@\") {\n        text = cap[0];\n        href = \"mailto:\" + text;\n      } else {\n        let prevCapZero;\n        do {\n          prevCapZero = cap[0];\n          cap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? \"\";\n        } while (prevCapZero !== cap[0]);\n        text = cap[0];\n        if (cap[1] === \"www.\") {\n          href = \"http://\" + cap[0];\n        } else {\n          href = cap[0];\n        }\n      }\n      return {\n        type: \"link\",\n        raw: cap[0],\n        text,\n        href,\n        tokens: [\n          {\n            type: \"text\",\n            raw: text,\n            text\n          }\n        ]\n      };\n    }\n  }\n  inlineText(src) {\n    const cap = this.rules.inline.text.exec(src);\n    if (cap) {\n      const escaped = this.lexer.state.inRawBlock;\n      return {\n        type: \"text\",\n        raw: cap[0],\n        text: cap[0],\n        escaped\n      };\n    }\n  }\n};\n\n// src/Lexer.ts\nvar _Lexer = class __Lexer {\n  tokens;\n  options;\n  state;\n  tokenizer;\n  inlineQueue;\n  constructor(options2) {\n    this.tokens = [];\n    this.tokens.links = /* @__PURE__ */ Object.create(null);\n    this.options = options2 || _defaults;\n    this.options.tokenizer = this.options.tokenizer || new _Tokenizer();\n    this.tokenizer = this.options.tokenizer;\n    this.tokenizer.options = this.options;\n    this.tokenizer.lexer = this;\n    this.inlineQueue = [];\n    this.state = {\n      inLink: false,\n      inRawBlock: false,\n      top: true\n    };\n    const rules = {\n      other,\n      block: block.normal,\n      inline: inline.normal\n    };\n    if (this.options.pedantic) {\n      rules.block = block.pedantic;\n      rules.inline = inline.pedantic;\n    } else if (this.options.gfm) {\n      rules.block = block.gfm;\n      if (this.options.breaks) {\n        rules.inline = inline.breaks;\n      } else {\n        rules.inline = inline.gfm;\n      }\n    }\n    this.tokenizer.rules = rules;\n  }\n  /**\n   * Expose Rules\n   */\n  static get rules() {\n    return {\n      block,\n      inline\n    };\n  }\n  /**\n   * Static Lex Method\n   */\n  static lex(src, options2) {\n    const lexer2 = new __Lexer(options2);\n    return lexer2.lex(src);\n  }\n  /**\n   * Static Lex Inline Method\n   */\n  static lexInline(src, options2) {\n    const lexer2 = new __Lexer(options2);\n    return lexer2.inlineTokens(src);\n  }\n  /**\n   * Preprocessing\n   */\n  lex(src) {\n    src = src.replace(other.carriageReturn, \"\\n\");\n    this.blockTokens(src, this.tokens);\n    for (let i = 0; i < this.inlineQueue.length; i++) {\n      const next = this.inlineQueue[i];\n      this.inlineTokens(next.src, next.tokens);\n    }\n    this.inlineQueue = [];\n    return this.tokens;\n  }\n  blockTokens(src, tokens = [], lastParagraphClipped = false) {\n    if (this.options.pedantic) {\n      src = src.replace(other.tabCharGlobal, \"    \").replace(other.spaceLine, \"\");\n    }\n    while (src) {\n      let token;\n      if (this.options.extensions?.block?.some((extTokenizer) => {\n        if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n          src = src.substring(token.raw.length);\n          tokens.push(token);\n          return true;\n        }\n        return false;\n      })) {\n        continue;\n      }\n      if (token = this.tokenizer.space(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (token.raw.length === 1 && lastToken !== void 0) {\n          lastToken.raw += \"\\n\";\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (token = this.tokenizer.code(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"paragraph\" || lastToken?.type === \"text\") {\n          lastToken.raw += \"\\n\" + token.raw;\n          lastToken.text += \"\\n\" + token.text;\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (token = this.tokenizer.fences(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.heading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.hr(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.blockquote(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.list(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.html(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.def(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"paragraph\" || lastToken?.type === \"text\") {\n          lastToken.raw += \"\\n\" + token.raw;\n          lastToken.text += \"\\n\" + token.raw;\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else if (!this.tokens.links[token.tag]) {\n          this.tokens.links[token.tag] = {\n            href: token.href,\n            title: token.title\n          };\n        }\n        continue;\n      }\n      if (token = this.tokenizer.table(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.lheading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      let cutSrc = src;\n      if (this.options.extensions?.startBlock) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startBlock.forEach((getStartIndex) => {\n          tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n          if (typeof tempStart === \"number\" && tempStart >= 0) {\n            startIndex = Math.min(startIndex, tempStart);\n          }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n        const lastToken = tokens.at(-1);\n        if (lastParagraphClipped && lastToken?.type === \"paragraph\") {\n          lastToken.raw += \"\\n\" + token.raw;\n          lastToken.text += \"\\n\" + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        lastParagraphClipped = cutSrc.length !== src.length;\n        src = src.substring(token.raw.length);\n        continue;\n      }\n      if (token = this.tokenizer.text(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"text\") {\n          lastToken.raw += \"\\n\" + token.raw;\n          lastToken.text += \"\\n\" + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (src) {\n        const errMsg = \"Infinite loop on byte: \" + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n    this.state.top = true;\n    return tokens;\n  }\n  inline(src, tokens = []) {\n    this.inlineQueue.push({ src, tokens });\n    return tokens;\n  }\n  /**\n   * Lexing/Compiling\n   */\n  inlineTokens(src, tokens = []) {\n    let maskedSrc = src;\n    let match = null;\n    if (this.tokens.links) {\n      const links = Object.keys(this.tokens.links);\n      if (links.length > 0) {\n        while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n          if (links.includes(match[0].slice(match[0].lastIndexOf(\"[\") + 1, -1))) {\n            maskedSrc = maskedSrc.slice(0, match.index) + \"[\" + \"a\".repeat(match[0].length - 2) + \"]\" + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n          }\n        }\n      }\n    }\n    while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + \"++\" + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n    }\n    while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + \"[\" + \"a\".repeat(match[0].length - 2) + \"]\" + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n    }\n    let keepPrevChar = false;\n    let prevChar = \"\";\n    while (src) {\n      if (!keepPrevChar) {\n        prevChar = \"\";\n      }\n      keepPrevChar = false;\n      let token;\n      if (this.options.extensions?.inline?.some((extTokenizer) => {\n        if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n          src = src.substring(token.raw.length);\n          tokens.push(token);\n          return true;\n        }\n        return false;\n      })) {\n        continue;\n      }\n      if (token = this.tokenizer.escape(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.tag(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.link(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (token.type === \"text\" && lastToken?.type === \"text\") {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.codespan(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.br(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.del(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.autolink(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      let cutSrc = src;\n      if (this.options.extensions?.startInline) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startInline.forEach((getStartIndex) => {\n          tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n          if (typeof tempStart === \"number\" && tempStart >= 0) {\n            startIndex = Math.min(startIndex, tempStart);\n          }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (token = this.tokenizer.inlineText(cutSrc)) {\n        src = src.substring(token.raw.length);\n        if (token.raw.slice(-1) !== \"_\") {\n          prevChar = token.raw.slice(-1);\n        }\n        keepPrevChar = true;\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"text\") {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (src) {\n        const errMsg = \"Infinite loop on byte: \" + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n    return tokens;\n  }\n};\n\n// src/Renderer.ts\nvar _Renderer = class {\n  options;\n  parser;\n  // set by the parser\n  constructor(options2) {\n    this.options = options2 || _defaults;\n  }\n  space(token) {\n    return \"\";\n  }\n  code({ text, lang, escaped }) {\n    const langString = (lang || \"\").match(other.notSpaceStart)?.[0];\n    const code = text.replace(other.endingNewline, \"\") + \"\\n\";\n    if (!langString) {\n      return \"<pre><code>\" + (escaped ? code : escape2(code, true)) + \"</code></pre>\\n\";\n    }\n    return '<pre><code class=\"language-' + escape2(langString) + '\">' + (escaped ? code : escape2(code, true)) + \"</code></pre>\\n\";\n  }\n  blockquote({ tokens }) {\n    const body = this.parser.parse(tokens);\n    return `<blockquote>\n${body}</blockquote>\n`;\n  }\n  html({ text }) {\n    return text;\n  }\n  heading({ tokens, depth }) {\n    return `<h${depth}>${this.parser.parseInline(tokens)}</h${depth}>\n`;\n  }\n  hr(token) {\n    return \"<hr>\\n\";\n  }\n  list(token) {\n    const ordered = token.ordered;\n    const start = token.start;\n    let body = \"\";\n    for (let j = 0; j < token.items.length; j++) {\n      const item = token.items[j];\n      body += this.listitem(item);\n    }\n    const type = ordered ? \"ol\" : \"ul\";\n    const startAttr = ordered && start !== 1 ? ' start=\"' + start + '\"' : \"\";\n    return \"<\" + type + startAttr + \">\\n\" + body + \"</\" + type + \">\\n\";\n  }\n  listitem(item) {\n    let itemBody = \"\";\n    if (item.task) {\n      const checkbox = this.checkbox({ checked: !!item.checked });\n      if (item.loose) {\n        if (item.tokens[0]?.type === \"paragraph\") {\n          item.tokens[0].text = checkbox + \" \" + item.tokens[0].text;\n          if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === \"text\") {\n            item.tokens[0].tokens[0].text = checkbox + \" \" + escape2(item.tokens[0].tokens[0].text);\n            item.tokens[0].tokens[0].escaped = true;\n          }\n        } else {\n          item.tokens.unshift({\n            type: \"text\",\n            raw: checkbox + \" \",\n            text: checkbox + \" \",\n            escaped: true\n          });\n        }\n      } else {\n        itemBody += checkbox + \" \";\n      }\n    }\n    itemBody += this.parser.parse(item.tokens, !!item.loose);\n    return `<li>${itemBody}</li>\n`;\n  }\n  checkbox({ checked }) {\n    return \"<input \" + (checked ? 'checked=\"\" ' : \"\") + 'disabled=\"\" type=\"checkbox\">';\n  }\n  paragraph({ tokens }) {\n    return `<p>${this.parser.parseInline(tokens)}</p>\n`;\n  }\n  table(token) {\n    let header = \"\";\n    let cell = \"\";\n    for (let j = 0; j < token.header.length; j++) {\n      cell += this.tablecell(token.header[j]);\n    }\n    header += this.tablerow({ text: cell });\n    let body = \"\";\n    for (let j = 0; j < token.rows.length; j++) {\n      const row = token.rows[j];\n      cell = \"\";\n      for (let k = 0; k < row.length; k++) {\n        cell += this.tablecell(row[k]);\n      }\n      body += this.tablerow({ text: cell });\n    }\n    if (body) body = `<tbody>${body}</tbody>`;\n    return \"<table>\\n<thead>\\n\" + header + \"</thead>\\n\" + body + \"</table>\\n\";\n  }\n  tablerow({ text }) {\n    return `<tr>\n${text}</tr>\n`;\n  }\n  tablecell(token) {\n    const content = this.parser.parseInline(token.tokens);\n    const type = token.header ? \"th\" : \"td\";\n    const tag2 = token.align ? `<${type} align=\"${token.align}\">` : `<${type}>`;\n    return tag2 + content + `</${type}>\n`;\n  }\n  /**\n   * span level renderer\n   */\n  strong({ tokens }) {\n    return `<strong>${this.parser.parseInline(tokens)}</strong>`;\n  }\n  em({ tokens }) {\n    return `<em>${this.parser.parseInline(tokens)}</em>`;\n  }\n  codespan({ text }) {\n    return `<code>${escape2(text, true)}</code>`;\n  }\n  br(token) {\n    return \"<br>\";\n  }\n  del({ tokens }) {\n    return `<del>${this.parser.parseInline(tokens)}</del>`;\n  }\n  link({ href, title, tokens }) {\n    const text = this.parser.parseInline(tokens);\n    const cleanHref = cleanUrl(href);\n    if (cleanHref === null) {\n      return text;\n    }\n    href = cleanHref;\n    let out = '<a href=\"' + href + '\"';\n    if (title) {\n      out += ' title=\"' + escape2(title) + '\"';\n    }\n    out += \">\" + text + \"</a>\";\n    return out;\n  }\n  image({ href, title, text, tokens }) {\n    if (tokens) {\n      text = this.parser.parseInline(tokens, this.parser.textRenderer);\n    }\n    const cleanHref = cleanUrl(href);\n    if (cleanHref === null) {\n      return escape2(text);\n    }\n    href = cleanHref;\n    let out = `<img src=\"${href}\" alt=\"${text}\"`;\n    if (title) {\n      out += ` title=\"${escape2(title)}\"`;\n    }\n    out += \">\";\n    return out;\n  }\n  text(token) {\n    return \"tokens\" in token && token.tokens ? this.parser.parseInline(token.tokens) : \"escaped\" in token && token.escaped ? token.text : escape2(token.text);\n  }\n};\n\n// src/TextRenderer.ts\nvar _TextRenderer = class {\n  // no need for block level renderers\n  strong({ text }) {\n    return text;\n  }\n  em({ text }) {\n    return text;\n  }\n  codespan({ text }) {\n    return text;\n  }\n  del({ text }) {\n    return text;\n  }\n  html({ text }) {\n    return text;\n  }\n  text({ text }) {\n    return text;\n  }\n  link({ text }) {\n    return \"\" + text;\n  }\n  image({ text }) {\n    return \"\" + text;\n  }\n  br() {\n    return \"\";\n  }\n};\n\n// src/Parser.ts\nvar _Parser = class __Parser {\n  options;\n  renderer;\n  textRenderer;\n  constructor(options2) {\n    this.options = options2 || _defaults;\n    this.options.renderer = this.options.renderer || new _Renderer();\n    this.renderer = this.options.renderer;\n    this.renderer.options = this.options;\n    this.renderer.parser = this;\n    this.textRenderer = new _TextRenderer();\n  }\n  /**\n   * Static Parse Method\n   */\n  static parse(tokens, options2) {\n    const parser2 = new __Parser(options2);\n    return parser2.parse(tokens);\n  }\n  /**\n   * Static Parse Inline Method\n   */\n  static parseInline(tokens, options2) {\n    const parser2 = new __Parser(options2);\n    return parser2.parseInline(tokens);\n  }\n  /**\n   * Parse Loop\n   */\n  parse(tokens, top = true) {\n    let out = \"\";\n    for (let i = 0; i < tokens.length; i++) {\n      const anyToken = tokens[i];\n      if (this.options.extensions?.renderers?.[anyToken.type]) {\n        const genericToken = anyToken;\n        const ret = this.options.extensions.renderers[genericToken.type].call({ parser: this }, genericToken);\n        if (ret !== false || ![\"space\", \"hr\", \"heading\", \"code\", \"table\", \"blockquote\", \"list\", \"html\", \"paragraph\", \"text\"].includes(genericToken.type)) {\n          out += ret || \"\";\n          continue;\n        }\n      }\n      const token = anyToken;\n      switch (token.type) {\n        case \"space\": {\n          out += this.renderer.space(token);\n          continue;\n        }\n        case \"hr\": {\n          out += this.renderer.hr(token);\n          continue;\n        }\n        case \"heading\": {\n          out += this.renderer.heading(token);\n          continue;\n        }\n        case \"code\": {\n          out += this.renderer.code(token);\n          continue;\n        }\n        case \"table\": {\n          out += this.renderer.table(token);\n          continue;\n        }\n        case \"blockquote\": {\n          out += this.renderer.blockquote(token);\n          continue;\n        }\n        case \"list\": {\n          out += this.renderer.list(token);\n          continue;\n        }\n        case \"html\": {\n          out += this.renderer.html(token);\n          continue;\n        }\n        case \"paragraph\": {\n          out += this.renderer.paragraph(token);\n          continue;\n        }\n        case \"text\": {\n          let textToken = token;\n          let body = this.renderer.text(textToken);\n          while (i + 1 < tokens.length && tokens[i + 1].type === \"text\") {\n            textToken = tokens[++i];\n            body += \"\\n\" + this.renderer.text(textToken);\n          }\n          if (top) {\n            out += this.renderer.paragraph({\n              type: \"paragraph\",\n              raw: body,\n              text: body,\n              tokens: [{ type: \"text\", raw: body, text: body, escaped: true }]\n            });\n          } else {\n            out += body;\n          }\n          continue;\n        }\n        default: {\n          const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n          if (this.options.silent) {\n            console.error(errMsg);\n            return \"\";\n          } else {\n            throw new Error(errMsg);\n          }\n        }\n      }\n    }\n    return out;\n  }\n  /**\n   * Parse Inline Tokens\n   */\n  parseInline(tokens, renderer = this.renderer) {\n    let out = \"\";\n    for (let i = 0; i < tokens.length; i++) {\n      const anyToken = tokens[i];\n      if (this.options.extensions?.renderers?.[anyToken.type]) {\n        const ret = this.options.extensions.renderers[anyToken.type].call({ parser: this }, anyToken);\n        if (ret !== false || ![\"escape\", \"html\", \"link\", \"image\", \"strong\", \"em\", \"codespan\", \"br\", \"del\", \"text\"].includes(anyToken.type)) {\n          out += ret || \"\";\n          continue;\n        }\n      }\n      const token = anyToken;\n      switch (token.type) {\n        case \"escape\": {\n          out += renderer.text(token);\n          break;\n        }\n        case \"html\": {\n          out += renderer.html(token);\n          break;\n        }\n        case \"link\": {\n          out += renderer.link(token);\n          break;\n        }\n        case \"image\": {\n          out += renderer.image(token);\n          break;\n        }\n        case \"strong\": {\n          out += renderer.strong(token);\n          break;\n        }\n        case \"em\": {\n          out += renderer.em(token);\n          break;\n        }\n        case \"codespan\": {\n          out += renderer.codespan(token);\n          break;\n        }\n        case \"br\": {\n          out += renderer.br(token);\n          break;\n        }\n        case \"del\": {\n          out += renderer.del(token);\n          break;\n        }\n        case \"text\": {\n          out += renderer.text(token);\n          break;\n        }\n        default: {\n          const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n          if (this.options.silent) {\n            console.error(errMsg);\n            return \"\";\n          } else {\n            throw new Error(errMsg);\n          }\n        }\n      }\n    }\n    return out;\n  }\n};\n\n// src/Hooks.ts\nvar _Hooks = class {\n  options;\n  block;\n  constructor(options2) {\n    this.options = options2 || _defaults;\n  }\n  static passThroughHooks = /* @__PURE__ */ new Set([\n    \"preprocess\",\n    \"postprocess\",\n    \"processAllTokens\"\n  ]);\n  /**\n   * Process markdown before marked\n   */\n  preprocess(markdown) {\n    return markdown;\n  }\n  /**\n   * Process HTML after marked is finished\n   */\n  postprocess(html2) {\n    return html2;\n  }\n  /**\n   * Process all tokens before walk tokens\n   */\n  processAllTokens(tokens) {\n    return tokens;\n  }\n  /**\n   * Provide function to tokenize markdown\n   */\n  provideLexer() {\n    return this.block ? _Lexer.lex : _Lexer.lexInline;\n  }\n  /**\n   * Provide function to parse tokens\n   */\n  provideParser() {\n    return this.block ? _Parser.parse : _Parser.parseInline;\n  }\n};\n\n// src/Instance.ts\nvar Marked = class {\n  defaults = _getDefaults();\n  options = this.setOptions;\n  parse = this.parseMarkdown(true);\n  parseInline = this.parseMarkdown(false);\n  Parser = _Parser;\n  Renderer = _Renderer;\n  TextRenderer = _TextRenderer;\n  Lexer = _Lexer;\n  Tokenizer = _Tokenizer;\n  Hooks = _Hooks;\n  constructor(...args) {\n    this.use(...args);\n  }\n  /**\n   * Run callback for every token\n   */\n  walkTokens(tokens, callback) {\n    let values = [];\n    for (const token of tokens) {\n      values = values.concat(callback.call(this, token));\n      switch (token.type) {\n        case \"table\": {\n          const tableToken = token;\n          for (const cell of tableToken.header) {\n            values = values.concat(this.walkTokens(cell.tokens, callback));\n          }\n          for (const row of tableToken.rows) {\n            for (const cell of row) {\n              values = values.concat(this.walkTokens(cell.tokens, callback));\n            }\n          }\n          break;\n        }\n        case \"list\": {\n          const listToken = token;\n          values = values.concat(this.walkTokens(listToken.items, callback));\n          break;\n        }\n        default: {\n          const genericToken = token;\n          if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n            this.defaults.extensions.childTokens[genericToken.type].forEach((childTokens) => {\n              const tokens2 = genericToken[childTokens].flat(Infinity);\n              values = values.concat(this.walkTokens(tokens2, callback));\n            });\n          } else if (genericToken.tokens) {\n            values = values.concat(this.walkTokens(genericToken.tokens, callback));\n          }\n        }\n      }\n    }\n    return values;\n  }\n  use(...args) {\n    const extensions = this.defaults.extensions || { renderers: {}, childTokens: {} };\n    args.forEach((pack) => {\n      const opts = { ...pack };\n      opts.async = this.defaults.async || opts.async || false;\n      if (pack.extensions) {\n        pack.extensions.forEach((ext) => {\n          if (!ext.name) {\n            throw new Error(\"extension name required\");\n          }\n          if (\"renderer\" in ext) {\n            const prevRenderer = extensions.renderers[ext.name];\n            if (prevRenderer) {\n              extensions.renderers[ext.name] = function(...args2) {\n                let ret = ext.renderer.apply(this, args2);\n                if (ret === false) {\n                  ret = prevRenderer.apply(this, args2);\n                }\n                return ret;\n              };\n            } else {\n              extensions.renderers[ext.name] = ext.renderer;\n            }\n          }\n          if (\"tokenizer\" in ext) {\n            if (!ext.level || ext.level !== \"block\" && ext.level !== \"inline\") {\n              throw new Error(\"extension level must be 'block' or 'inline'\");\n            }\n            const extLevel = extensions[ext.level];\n            if (extLevel) {\n              extLevel.unshift(ext.tokenizer);\n            } else {\n              extensions[ext.level] = [ext.tokenizer];\n            }\n            if (ext.start) {\n              if (ext.level === \"block\") {\n                if (extensions.startBlock) {\n                  extensions.startBlock.push(ext.start);\n                } else {\n                  extensions.startBlock = [ext.start];\n                }\n              } else if (ext.level === \"inline\") {\n                if (extensions.startInline) {\n                  extensions.startInline.push(ext.start);\n                } else {\n                  extensions.startInline = [ext.start];\n                }\n              }\n            }\n          }\n          if (\"childTokens\" in ext && ext.childTokens) {\n            extensions.childTokens[ext.name] = ext.childTokens;\n          }\n        });\n        opts.extensions = extensions;\n      }\n      if (pack.renderer) {\n        const renderer = this.defaults.renderer || new _Renderer(this.defaults);\n        for (const prop in pack.renderer) {\n          if (!(prop in renderer)) {\n            throw new Error(`renderer '${prop}' does not exist`);\n          }\n          if ([\"options\", \"parser\"].includes(prop)) {\n            continue;\n          }\n          const rendererProp = prop;\n          const rendererFunc = pack.renderer[rendererProp];\n          const prevRenderer = renderer[rendererProp];\n          renderer[rendererProp] = (...args2) => {\n            let ret = rendererFunc.apply(renderer, args2);\n            if (ret === false) {\n              ret = prevRenderer.apply(renderer, args2);\n            }\n            return ret || \"\";\n          };\n        }\n        opts.renderer = renderer;\n      }\n      if (pack.tokenizer) {\n        const tokenizer = this.defaults.tokenizer || new _Tokenizer(this.defaults);\n        for (const prop in pack.tokenizer) {\n          if (!(prop in tokenizer)) {\n            throw new Error(`tokenizer '${prop}' does not exist`);\n          }\n          if ([\"options\", \"rules\", \"lexer\"].includes(prop)) {\n            continue;\n          }\n          const tokenizerProp = prop;\n          const tokenizerFunc = pack.tokenizer[tokenizerProp];\n          const prevTokenizer = tokenizer[tokenizerProp];\n          tokenizer[tokenizerProp] = (...args2) => {\n            let ret = tokenizerFunc.apply(tokenizer, args2);\n            if (ret === false) {\n              ret = prevTokenizer.apply(tokenizer, args2);\n            }\n            return ret;\n          };\n        }\n        opts.tokenizer = tokenizer;\n      }\n      if (pack.hooks) {\n        const hooks = this.defaults.hooks || new _Hooks();\n        for (const prop in pack.hooks) {\n          if (!(prop in hooks)) {\n            throw new Error(`hook '${prop}' does not exist`);\n          }\n          if ([\"options\", \"block\"].includes(prop)) {\n            continue;\n          }\n          const hooksProp = prop;\n          const hooksFunc = pack.hooks[hooksProp];\n          const prevHook = hooks[hooksProp];\n          if (_Hooks.passThroughHooks.has(prop)) {\n            hooks[hooksProp] = (arg) => {\n              if (this.defaults.async) {\n                return Promise.resolve(hooksFunc.call(hooks, arg)).then((ret2) => {\n                  return prevHook.call(hooks, ret2);\n                });\n              }\n              const ret = hooksFunc.call(hooks, arg);\n              return prevHook.call(hooks, ret);\n            };\n          } else {\n            hooks[hooksProp] = (...args2) => {\n              let ret = hooksFunc.apply(hooks, args2);\n              if (ret === false) {\n                ret = prevHook.apply(hooks, args2);\n              }\n              return ret;\n            };\n          }\n        }\n        opts.hooks = hooks;\n      }\n      if (pack.walkTokens) {\n        const walkTokens2 = this.defaults.walkTokens;\n        const packWalktokens = pack.walkTokens;\n        opts.walkTokens = function(token) {\n          let values = [];\n          values.push(packWalktokens.call(this, token));\n          if (walkTokens2) {\n            values = values.concat(walkTokens2.call(this, token));\n          }\n          return values;\n        };\n      }\n      this.defaults = { ...this.defaults, ...opts };\n    });\n    return this;\n  }\n  setOptions(opt) {\n    this.defaults = { ...this.defaults, ...opt };\n    return this;\n  }\n  lexer(src, options2) {\n    return _Lexer.lex(src, options2 ?? this.defaults);\n  }\n  parser(tokens, options2) {\n    return _Parser.parse(tokens, options2 ?? this.defaults);\n  }\n  parseMarkdown(blockType) {\n    const parse2 = (src, options2) => {\n      const origOpt = { ...options2 };\n      const opt = { ...this.defaults, ...origOpt };\n      const throwError = this.onError(!!opt.silent, !!opt.async);\n      if (this.defaults.async === true && origOpt.async === false) {\n        return throwError(new Error(\"marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.\"));\n      }\n      if (typeof src === \"undefined\" || src === null) {\n        return throwError(new Error(\"marked(): input parameter is undefined or null\"));\n      }\n      if (typeof src !== \"string\") {\n        return throwError(new Error(\"marked(): input parameter is of type \" + Object.prototype.toString.call(src) + \", string expected\"));\n      }\n      if (opt.hooks) {\n        opt.hooks.options = opt;\n        opt.hooks.block = blockType;\n      }\n      const lexer2 = opt.hooks ? opt.hooks.provideLexer() : blockType ? _Lexer.lex : _Lexer.lexInline;\n      const parser2 = opt.hooks ? opt.hooks.provideParser() : blockType ? _Parser.parse : _Parser.parseInline;\n      if (opt.async) {\n        return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src).then((src2) => lexer2(src2, opt)).then((tokens) => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens).then((tokens) => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens).then((tokens) => parser2(tokens, opt)).then((html2) => opt.hooks ? opt.hooks.postprocess(html2) : html2).catch(throwError);\n      }\n      try {\n        if (opt.hooks) {\n          src = opt.hooks.preprocess(src);\n        }\n        let tokens = lexer2(src, opt);\n        if (opt.hooks) {\n          tokens = opt.hooks.processAllTokens(tokens);\n        }\n        if (opt.walkTokens) {\n          this.walkTokens(tokens, opt.walkTokens);\n        }\n        let html2 = parser2(tokens, opt);\n        if (opt.hooks) {\n          html2 = opt.hooks.postprocess(html2);\n        }\n        return html2;\n      } catch (e) {\n        return throwError(e);\n      }\n    };\n    return parse2;\n  }\n  onError(silent, async) {\n    return (e) => {\n      e.message += \"\\nPlease report this to https://github.com/markedjs/marked.\";\n      if (silent) {\n        const msg = \"<p>An error occurred:</p><pre>\" + escape2(e.message + \"\", true) + \"</pre>\";\n        if (async) {\n          return Promise.resolve(msg);\n        }\n        return msg;\n      }\n      if (async) {\n        return Promise.reject(e);\n      }\n      throw e;\n    };\n  }\n};\n\n// src/marked.ts\nvar markedInstance = new Marked();\nfunction marked(src, opt) {\n  return markedInstance.parse(src, opt);\n}\nmarked.options = marked.setOptions = function(options2) {\n  markedInstance.setOptions(options2);\n  marked.defaults = markedInstance.defaults;\n  changeDefaults(marked.defaults);\n  return marked;\n};\nmarked.getDefaults = _getDefaults;\nmarked.defaults = _defaults;\nmarked.use = function(...args) {\n  markedInstance.use(...args);\n  marked.defaults = markedInstance.defaults;\n  changeDefaults(marked.defaults);\n  return marked;\n};\nmarked.walkTokens = function(tokens, callback) {\n  return markedInstance.walkTokens(tokens, callback);\n};\nmarked.parseInline = markedInstance.parseInline;\nmarked.Parser = _Parser;\nmarked.parser = _Parser.parse;\nmarked.Renderer = _Renderer;\nmarked.TextRenderer = _TextRenderer;\nmarked.Lexer = _Lexer;\nmarked.lexer = _Lexer.lex;\nmarked.Tokenizer = _Tokenizer;\nmarked.Hooks = _Hooks;\nmarked.parse = marked;\nvar options = marked.options;\nvar setOptions = marked.setOptions;\nvar use = marked.use;\nvar walkTokens = marked.walkTokens;\nvar parseInline = marked.parseInline;\nvar parse = marked;\nvar parser = _Parser.parse;\nvar lexer = _Lexer.lex;\n\n//# sourceMappingURL=marked.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/marked/lib/marked.esm.js\n");

/***/ })

};
;