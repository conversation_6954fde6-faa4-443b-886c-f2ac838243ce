export interface ChatMessage {
  id: string;
  content: {
    text: string;
    echarts?: any; // Dados de configuração do ECharts
    attachments?: FileAttachment[]; // NOVO - Arquivos anexados
  };
  role: "user" | "assistant" | "system";
  timestamp: string;
}

// Novos tipos para suporte a arquivos
export interface FileAttachment {
  file: File;
  url: string;
  base64?: string;
  mimeType: string;
}

export interface InlineData {
  displayName: string;
  data: string; // base64
  mimeType: string;
}

export interface ApiResponsePart {
  text?: string;
  inlineData?: InlineData; // NOVO - Dados de arquivo inline
  functionCall?: {
    name: string;
    args: {
      [key: string]: any;
    };
  };
  functionResponse?: {
    name: string;
    response: {
      result: string;
    };
  };
}

export interface ApiResponseContent {
  parts: ApiResponsePart[];
  role: "user" | "model";
}

export interface ApiResponse {
  id: string;
  content: ApiResponseContent;
  timestamp: number;
  invocation_id: string;
  author: string;
}