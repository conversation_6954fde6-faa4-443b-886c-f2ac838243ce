// Definição das bases de dados disponíveis para exportação
export interface Database {
  id: string;
  name: string;
  endpoint: string;
  description?: string;
}

// Tipos para o contexto de exportação
export interface ExportContextProps {
  databases: Database[];
  selectedDatabases: string[];
  toggleDatabase: (id: string) => void;
  toggleSelectAll: () => void;
  allSelected: boolean;
  isExporting: boolean;
  exportData: (dataReferencia?: string) => Promise<void>;
}

// Tipos para a resposta da API
export interface ApiResponse<T> {
  data: T[];
  error?: string;
} 